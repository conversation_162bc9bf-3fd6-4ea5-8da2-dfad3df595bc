@keyframes shimmer {
	0% {
		opacity: 0;
		transform: translateX(-100%);
	}
	50% {
		opacity: 0.8;
		transform: translateX(0%);
	}
	100% {
		opacity: 0;
		transform: translateX(100%);
	}
}

.animate-shimmer {
	animation: shimmer 3s ease-in-out infinite;
	animation-delay: 1s;
}

.title-container:hover .shimmer-line {
	animation-duration: 1.5s;
}

@keyframes subtleGlow {
	0%,
	100% {
		opacity: 0.3;
	}
	50% {
		opacity: 0.6;
	}
}

.subtle-glow {
	animation: subtleGlow 4s ease-in-out infinite;
}
