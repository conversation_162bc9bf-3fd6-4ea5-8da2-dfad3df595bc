import ImageBubble from "@/shared/assets/utils/bubble.webp";

export const LoginDescription = () => {
	return (
		<div className="text-center space-y-4 sm:space-y-6 lg:block hidden">
			<div className="flex justify-center">
				<div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[#0197B2] to-[#016d85] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg">
					<img src={ImageBubble} alt="Logo" className="w-6 h-6 sm:w-8 sm:h-8" />
				</div>
			</div>
			<div className="space-y-2 sm:space-y-3">
				<h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 tracking-tight">Bem-vindo de volta!</h2>
				<p className="text-gray-600 text-base sm:text-lg">Faça login para acessar sua conta</p>
			</div>
		</div>
	);
};
