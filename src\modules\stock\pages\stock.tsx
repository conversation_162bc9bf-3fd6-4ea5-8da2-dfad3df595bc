import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";

import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { Home } from "lucide-react";
import { IoAdd } from "react-icons/io5";
import { useNavigate } from "@tanstack/react-router";
import { StockFiltersSection } from "../components/find-all/filters";
import { TableStock } from "../components/find-all/table";
import { STOCK_CONFIG } from "../data/stock-config";
import { useStockFindAllFiltersForm } from "../hooks/stock/find-all-filters-form.hook";
import { useGetFindAllStock } from "../hooks/stock/get-find-all.hook";

const StockPage = () => {
	const navigate = useNavigate();
	const methods = useStockFindAllFiltersForm();
	const { watch } = methods;
	const watchedFilters = watch();
	const debouncedFilters = useDebounce<typeof watchedFilters>(watchedFilters, 300);
	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination();

	const { data, isLoading } = useGetFindAllStock({
		limit: itemsPerPage,
		page: page,
		invoice: debouncedFilters.invoice || undefined,
		expirationDate: debouncedFilters.expirationDate || undefined,
		status: debouncedFilters.status === undefined || debouncedFilters.status === "" ? undefined : debouncedFilters.status === "true",
	});

	return (
		<MainSkeletonContainer
			iconTitle={STOCK_CONFIG.subItems?.find(item => item.id === "stock-overview")?.Icon}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{
				href: STOCK_CONFIG?.subItems?.find(item => item.id === "stock-overview")?.path || "",
				label: "Estoque",
				icon: STOCK_CONFIG.Icon,
			}}
			pageTitle="Visão geral"
		>
			<StockFiltersSection methods={methods} />
			<TableStock
				data={data}
				isLoading={isLoading}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
			<button
				onClick={() => navigate({ to: "/estoque/adicionar" })}
				className="md:hidden fixed bottom-24 right-4 z-[60] bg-mainColor text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl border-4 border-white hover:scale-105 active:scale-95 transition-all duration-200"
				aria-label="Adicionar novo estoque"
			>
				<IoAdd size={28} />
			</button>
		</MainSkeletonContainer>
	);
};

export default StockPage;
