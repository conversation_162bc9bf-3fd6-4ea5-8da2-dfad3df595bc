import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { usePaginatedData } from "@/shared/hooks/utils/use-paginated-data.hook";
import { AnimatePresence, motion } from "framer-motion";
import { Home, Receipt } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { CreateTransactionModal } from "../components/transactions/create-transaction-modal";
import { TransactionSummaryCards } from "../components/transactions/resume-card";
import { TransactionFilters } from "../components/transactions/transaction-filters";
import { TransactionsListMobile } from "../components/transactions/transactions-list-mobile";
import { TransactionsTable } from "../components/transactions/transactions-table";
import { FINANCIAL_CONFIG, FINANCIAL_SUBITEM_IDS } from "../data/financial-config";
import { useFindAllTransactions } from "../hooks/transactions/find-all.hook";

interface TransactionFilters {
	description: string;
	date: string;
	accountId: string;
}

export const TransactionsListPage = () => {
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const methods = useForm<TransactionFilters>({
		defaultValues: {
			description: "",
			date: "",
			accountId: "",
		},
	});

	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination(50);

	const filters = methods.watch();

	const { transactions, isLoading } = useFindAllTransactions({
		page,
		limit: itemsPerPage,
		description: filters.description || undefined,
		date: filters.date || undefined,
		accountId: filters.accountId ? Number(filters.accountId) : undefined,
	});
	const { paginationData } = usePaginatedData({
		data: { success: true, data: transactions, status: 200 },
		itemsPerPage,
	});

	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		setError(null);
	}, [page, itemsPerPage, filters]);

	useEffect(() => {
		if (!isLoading && !transactions && !error) {
			setError("Erro ao carregar transações");
		}
	}, [isLoading, transactions, error]);

	let tableKey = "table";
	if (isLoading) {
		tableKey = "loading";
	} else if (error) {
		tableKey = "error";
	} else if (!transactions?.data?.length) {
		tableKey = "notfound";
	}

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<div className="flex justify-center items-center py-10 animate-pulse">
					<span className="text-gray-400 text-lg">Carregando transações...</span>
				</div>
			);
		}
		if (error) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-red-500 text-lg font-semibold mb-2">Erro ao carregar transações</span>
					<span className="text-gray-500 text-sm">Tente novamente mais tarde.</span>
				</div>
			);
		}
		if (!transactions?.data?.length) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-gray-500 text-lg font-semibold mb-2">Nenhuma transação encontrada</span>
					<span className="text-gray-400 text-sm">Ajuste os filtros ou tente novamente.</span>
				</div>
			);
		}
		return (
			<>
				<TransactionsTable transactions={transactions.data} />
				<TransactionsListMobile transactions={transactions.data} />
			</>
		);
	};

	return (
		<MainSkeletonContainer
			pageTitle="Transações"
			iconTitle={FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.TRANSACOES)?.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{
					href: FINANCIAL_CONFIG.path,
					label: "Financeiro",
					icon: FINANCIAL_CONFIG.Icon,
				},
			]}
			currentBreadcrumb={{
				href: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.TRANSACOES)?.path ?? "",
				label: "Transações",
				icon: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.TRANSACOES)?.Icon,
			}}
		>
			{" "}
			{/* Cards de Resumo */}
			<TransactionSummaryCards />
			<TransactionFilters methods={methods} onNewTransaction={() => setIsCreateModalOpen(true)} />
			<CardLMPContainer
				title="Lista de Transações"
				description="Acompanhe todas as transações realizadas"
				icon={<Receipt size={22} className="text-gray-500" />}
			>
				<AnimatePresence mode="wait">
					<motion.div
						key={tableKey}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2 }}
					>
						{renderTableContent()}
					</motion.div>
				</AnimatePresence>
				<Pagination
					paginationData={paginationData}
					itemsPerPage={itemsPerPage}
					page={page}
					onPageChange={handlePageChange}
					onItemsPerPageChange={handleItemsPerPageChange}
				/>
			</CardLMPContainer>{" "}
			<button
				onClick={() => setIsCreateModalOpen(true)}
				className="md:hidden fixed bottom-24 right-4 z-[60] bg-mainColor text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl border-4 border-white hover:scale-105 active:scale-95 transition-all duration-200"
				aria-label="Adicionar nova transação"
			>
				<IoAdd size={28} />
			</button>
			<CreateTransactionModal isOpen={isCreateModalOpen} onClose={() => setIsCreateModalOpen(false)} />
		</MainSkeletonContainer>
	);
};
