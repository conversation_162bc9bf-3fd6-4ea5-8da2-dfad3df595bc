import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { getCouponRequest } from "../api/requests/coupon";
import { ORDERS_MUTATION_KEYS } from "../data/query-keys";

export interface IUseCouponMutationProps {
	onSuccess?: (file: File) => void;
}

export const useCouponMutation = ({ onSuccess }: IUseCouponMutationProps = {}) => {
	const couponMutation = useMutation({
		mutationKey: ORDERS_MUTATION_KEYS.GET_COUPON,
		mutationFn: async (orderId: number) => {
			const response = await getCouponRequest(orderId);
			if (response.success) {
				return response.data;
			}
			throw new Error(response.data.message);
		},
		onMutate: () => {
			const pendingToastId = toast.loading("Buscando cupom fiscal...");
			return { pendingToastId };
		},
		onSuccess: (data, _, context) => {
			if (context?.pendingToastId) {
				// toast.success("Cupom fiscal obtido com sucesso!", { id: context.pendingToastId });
				toast.dismiss(context.pendingToastId);
			}
			onSuccess?.(data);
		},
		onError: (error: Error, _, context) => {
			if (context?.pendingToastId) {
				toast.error(error.message, { id: context.pendingToastId });
			}
		},
	});

	return {
		getCoupon: couponMutation.mutateAsync,
		isLoading: couponMutation.isPending,
		couponMutation,
	};
};
