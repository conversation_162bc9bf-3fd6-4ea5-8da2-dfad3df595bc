import { CardLMPContainer } from "@/shared/components/custom/card";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { useNavigate } from "@tanstack/react-router";
import { Filter, RefreshCw, Search } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { IProductFindAllFilters } from "../../../validators/product/product-find-all-filters.validator";

interface FiltersSectionProps {
	methods: UseFormReturn<IProductFindAllFilters>;
}

export const FiltersSection = ({ methods }: FiltersSectionProps) => {
	const { reset } = methods;
	const navigate = useNavigate();

	const handleReset = () => {
		reset({
			filter: "",
		});
	};

	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<Button
					onClick={() =>
						navigate({
							to: "/produtos/adicionar",
						})
					}
					className="hidden md:flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
				>
					<IoAdd size={18} />
					Adicionar
				</Button>
			}
		>
			<form className="flex flex-col gap-3 md:flex-row md:items-end">
				<div className="w-full">
					<label className="block text-sm font-medium text-gray-600 mb-1">Buscar</label>
					<div className="relative">
						<Input
							type="text"
							placeholder="Digite o nome ou código de barras do produto..."
							className="w-full h-[45px] rounded-[10px] pl-10"
							{...methods.register("filter")}
						/>
						<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>{" "}
				<div className="w-full md:w-auto">
					<Button
						type="button"
						variant="outline"
						onClick={handleReset}
						className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2"
					>
						<RefreshCw size={18} className="text-mainColor" />
						<span>Resetar Filtros</span>
					</Button>
				</div>
			</form>
			<div className="md:hidden mt-3 pt-3 border-t border-gray-100">
				<Button
					onClick={() =>
						navigate({
							to: "/produtos/adicionar",
						})
					}
					className="w-full flex items-center justify-center gap-2 bg-mainColor text-white h-[45px] rounded-[10px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
				>
					<IoAdd size={18} />
					Adicionar Produto
				</Button>
			</div>
		</CardLMPContainer>
	);
};
