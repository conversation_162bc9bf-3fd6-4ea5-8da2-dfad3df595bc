import { motion } from "framer-motion";
import { Tag, User } from "lucide-react";
import { IPerson } from "../dtos/find-all.dto";

interface PersonsListMobileProps {
	persons: IPerson[];
}

export const PersonsListMobile = ({ persons }: PersonsListMobileProps) => {
	const getClassificationBadge = (classification: string) => {
		switch (classification.toLowerCase()) {
			case "cliente":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
					>
						<User size={12} className="mr-1" />
						{classification}
					</span>
				);
			case "fornecedor":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
					>
						<Tag size={12} className="mr-1" />
						{classification}
					</span>
				);
			default:
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
					>
						{classification}
					</span>
				);
		}
	};

	return (
		<div className="md:hidden space-y-4 pb-32">
			{persons.map((person, index) => (
				<motion.div
					key={person.id}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.2, delay: index * 0.05 }}
					className="w-full rounded-xl border border-gray-100 bg-white p-5 shadow-sm hover:shadow-md transition-all duration-200"
				>
					{" "}
					{/* Header com nome e informações */}
					<div className="mb-3">
						<h3 className="font-semibold text-gray-900 text-lg leading-6 mb-1">{person.name}</h3>
						<p className="text-sm text-gray-500">ID: {person.id}</p>
					</div>
					{/* Classificações */}
					<div className="flex flex-wrap gap-2">
						{person.classifications?.length ? (
							person.classifications.map(classification => getClassificationBadge(classification))
						) : (
							<span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-500">
								Sem classificação
							</span>
						)}
					</div>
				</motion.div>
			))}

			{/* Espaço para não ficar atrás do footer */}
			<div className="h-4" />
		</div>
	);
};
