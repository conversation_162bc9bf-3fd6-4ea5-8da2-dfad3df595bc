import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IOrderDetailedDto } from "../../dtos/find-all-orders.dto";
import { ORDERS_ENDPOINTS } from "../endpoints";

export const findOrderDetailedByIdRequest = async (id: number): Promise<ApiResponse<IOrderDetailedDto>> => {
	return createRequest({
		path: ORDERS_ENDPOINTS.FIND_DETAILED_BY_ID(id),
		method: "GET",
	});
};
