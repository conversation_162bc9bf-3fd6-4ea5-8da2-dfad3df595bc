import { useAtom } from "jotai";
import React, { useEffect, useState } from "react";

import { persistedStockAtom } from "../../states/create-stock.state";
import { ICreateStock } from "../../validators/create-stock.validator";

interface ResumePopupProps {
	onResume: (data: ICreateStock) => void;
}

export const ResumePopup: React.FC<ResumePopupProps> = ({ onResume }) => {
	const [persistedData, setPersistedData] = useAtom(persistedStockAtom);
	const [initialData, setInitialData] = useState<ICreateStock | null>(null);
	const [visible, setVisible] = useState(false);

	useEffect(() => {
		if (persistedData != null && initialData === null) {
			setInitialData(persistedData);
			setVisible(true);
		}
	}, [persistedData, initialData]);

	if (!visible || initialData == null) {
		return null;
	}

	const handleResume = () => {
		setVisible(false);
		setPersistedData(null);
		onResume(initialData);
	};

	const handleClose = () => {
		setVisible(false);
		setPersistedData(null);
	};

	return (
		<div
			style={{
				position: "fixed",
				top: "20px",
				right: "20px",
				backgroundColor: "#fff",
				border: "1px solid #ccc",
				borderRadius: "4px",
				padding: "10px 15px",
				boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
				zIndex: 1000,
			}}
		>
			<p style={{ margin: 0 }}>Você estava editando o formulário. Deseja retomar?</p>
			<button
				onClick={handleResume}
				style={{
					marginTop: "5px",
					background: "#007bff",
					color: "#fff",
					border: "none",
					padding: "5px 10px",
					borderRadius: "4px",
					cursor: "pointer",
				}}
			>
				Retomar
			</button>
			<button
				onClick={handleClose}
				style={{
					marginTop: "5px",
					background: "#dc3545",
					color: "#fff",
					border: "none",
					padding: "5px 10px",
					borderRadius: "4px",
					cursor: "pointer",
				}}
			>
				Não
			</button>
		</div>
	);
};
