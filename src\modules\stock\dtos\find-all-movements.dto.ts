import { IPaginatedResponse, IPaginationData } from "@/shared/types/requests/requests.type";

export interface IMovementItem {
	id: number;
	product: string;
	quantity: number;
	createdAt: string;
	description: string;
}

export interface IFindAllMovementsDTO extends IPaginatedResponse<IMovementItem> {
	pagination: IPaginationData & {
		totalItems: number;
		itemsPerPage: number;
		totalPages: number;
		currentPage: number;
	};
}

export interface IFindAllMovementsFiltersDto {
	page: number;
	limit: number;
	search?: string;
	date?: string;
}
