import { IPaginatedResponse } from "@/shared/types/requests/requests.type";

export interface IProductListDTO {
	id: number;
	name: string;
	quantity: number;
	price: number;
	status: boolean;
	barcode: string;
	category?: string;
}

export interface IProductList extends IPaginatedResponse<IProductListDTO> {
	total: number;
}

export interface IProductListProps {
	page: number;
	limit: number;
	filter?: string;
}
