import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { IOverlayContainerProps } from "@/shared/types/containers/overlay-container.type";
import { motion } from "framer-motion";

const containerVariants = {
	hidden: { y: 30, opacity: 0 },
	visible: {
		y: 0,
		opacity: 1,
		transition: {
			duration: 0.3,
			ease: "easeOut",
		},
	},
	exit: {
		y: 30,
		opacity: 0,
		transition: {
			duration: 0.2,
			ease: "easeIn",
		},
	},
};

export function MenuOverlay({ isVisible, onClose, children }: IOverlayContainerProps) {
	return (
		<OverlayContainer isVisible={isVisible} onClose={onClose}>
			<motion.div
				className="relative bg-white/95 backdrop-blur-md w-[95%] max-w-2xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden"
				variants={containerVariants}
				initial="hidden"
				animate="visible"
				exit="exit"
				onClick={e => e.stopPropagation()}
			>
				<div className="p-6">{children}</div>
			</motion.div>
		</OverlayContainer>
	);
}
