import { sidebarTransitions } from "@/layout/data/constants/sidebar-transitions";
import { SidebarItems } from "@/layout/hooks/sidebar/get-items.hook";
import { useItemsSidebar } from "@/layout/hooks/sidebar/items-sidebar.hook";
import { useSidebarMobile } from "@/layout/hooks/sidebar/sidebar-mobile.hook";
import { IItemSidebar, ISubItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { shouldNavigateDirectly } from "@/shared/routes/module-routes.factory";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { MobileBottomBar } from "./footer-siderbar";
import { MenuItemGrid } from "./menu-item-grid";
import { MenuOverlay } from "./mobile-overlay";

const getItems = new SidebarItems();

export function SidebarMobile() {
	const { isMenuVisible, toggleMenu, closeMenu } = useSidebarMobile();
	const { itemActive, handleItemActive, menuItems, configItems, otherItems } = useItemsSidebar(getItems);

	const [selectedSubItems, setSelectedSubItems] = useState<IItemSidebar | ISubItemSidebar | null>(null);

	const handleItemClick = (item: IItemSidebar | ISubItemSidebar) => {
		const hasSubItems = item.subItems && item.subItems.length > 0;
		const shouldNavigateDirect = shouldNavigateDirectly(item.subItems);

		if (hasSubItems && !shouldNavigateDirect) {
			setSelectedSubItems(item);
			return;
		}
		const targetSubItem = item.subItems?.length === 1 ? item.subItems[0] : undefined;
		if (targetSubItem) {
			handleItemActive(item as IItemSidebar, targetSubItem);
		} else {
			handleItemActive(item as IItemSidebar);
		}
		closeMenu();
		setSelectedSubItems(null);
	};

	const handleBack = () => {
		setSelectedSubItems(null);
	};
	return (
		<>
			<MobileBottomBar onToggleMenu={toggleMenu} getItems={getItems} />
			<MenuOverlay isVisible={isMenuVisible} onClose={closeMenu}>
				<div className="w-full max-w-lg min-h-[300px] max-h-[85vh] flex flex-col">
					<AnimatePresence mode="wait">
						{selectedSubItems ? (
							<motion.div
								key="subItemsView"
								variants={sidebarTransitions}
								initial="enter"
								animate="center"
								exit="exit"
								className="w-full flex flex-col flex-1"
							>
								<div className="flex items-center mb-6 pb-4 border-b border-gray-200">
									<motion.div
										onClick={handleBack}
										className="p-2 mr-3 cursor-pointer rounded-full hover:bg-gray-100 transition-colors"
										whileHover={{ scale: 1.02 }}
										whileTap={{ scale: 0.98 }}
									>
										<ArrowLeft size={20} className="text-gray-600" />
									</motion.div>
									<div className="flex flex-col">
										<span className="text-lg font-semibold text-gray-800">{selectedSubItems.label}</span>
										<span className="text-sm text-gray-500">Selecione uma opção</span>
									</div>
								</div>
								<div className="flex-1 overflow-y-auto">
									<div className="grid grid-cols-2 gap-3 pb-4">
										<MenuItemGrid items={selectedSubItems.subItems || []} itemActive={itemActive} onItemClick={handleItemClick} />
									</div>
								</div>
							</motion.div>
						) : (
							<motion.div
								key="mainItemsView"
								variants={sidebarTransitions}
								initial="enter"
								animate="center"
								exit="exit"
								className="w-full flex flex-col flex-1"
							>
								<div className="mb-6 pb-4 border-b border-gray-200">
									<h2 className="text-lg font-semibold text-gray-800">Menu Principal</h2>
									<p className="text-sm text-gray-500">Acesse rapidamente suas funcionalidades</p>
								</div>
								<div className="flex-1 overflow-y-auto">
									<div className="space-y-6 pb-4">
										{menuItems.length > 0 && (
											<div>
												<h3 className="text-sm font-medium text-gray-600 mb-3 px-1">Módulos Principais</h3>
												<div className="grid grid-cols-2 gap-3">
													<MenuItemGrid items={menuItems} itemActive={itemActive} onItemClick={handleItemClick} />
												</div>
											</div>
										)}
										{configItems.length > 0 && (
											<div>
												<h3 className="text-sm font-medium text-gray-600 mb-3 px-1">Ferramentas</h3>
												<div className="grid grid-cols-2 gap-3">
													<MenuItemGrid items={configItems} itemActive={itemActive} onItemClick={handleItemClick} />
												</div>
											</div>
										)}
										{otherItems.length > 0 && (
											<div>
												<h3 className="text-sm font-medium text-gray-600 mb-3 px-1">Outros</h3>
												<div className="grid grid-cols-2 gap-3">
													<MenuItemGrid
														items={otherItems}
														itemActive={itemActive}
														onItemClick={handleItemClick}
														activeClassName="bg-mainColor text-white"
														inactiveClassName="bg-gray-100 hover:bg-gray-200 text-gray-600"
													/>
												</div>
											</div>
										)}
									</div>
								</div>
							</motion.div>
						)}
					</AnimatePresence>
				</div>
			</MenuOverlay>
		</>
	);
}
