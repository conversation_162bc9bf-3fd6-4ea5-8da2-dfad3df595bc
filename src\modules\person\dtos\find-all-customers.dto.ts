import { IPaginatedResponse } from "@/shared/types/requests/requests.type";

export interface ICustomerDTO {
	id: number;
	name: string;
	cnpjCpf: string;
	email: string;
	phone: string;
}

export interface IFindAllCustomersResponse extends IPaginatedResponse<ICustomerDTO> {
	total: number;
}

export interface IFindAllCustomersDto {
	page: number;
	limit: number;
	name?: string;
	document?: string;
}
