import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { AnimatePresence, motion } from "framer-motion";
import { Home, Users } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { IoAdd } from "react-icons/io5";
import { FiltersSection } from "../components/category/filters/filters-section";
import { CreateCategoryModal } from "../components/category/table/create-category-modal";
import { CreateGroupModal } from "../components/group/create-group-modal";
import { TableCategories } from "../components/category/table/table-categories";
import { PRODUCT_CONFIG, PRODUCT_SUBITEM_IDS } from "../data/product-config";
import { useCategoryFilters } from "../hooks/category/use-category-filters.hook";

const CategoryPage = () => {
	const methods = useCategoryFilters();
	const { page, itemsPerPage, handleItemsPerPageChange, handlePageChange } = usePagination();
	const categoryItem = PRODUCT_CONFIG.subItems?.find(item => item.id === PRODUCT_SUBITEM_IDS.CATEGORIES);
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	const [isCreateCategoryModalOpen, setIsCreateCategoryModalOpen] = useState(false);
	const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);
	const menuRef = useRef<HTMLDivElement>(null);
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				setIsMenuOpen(false);
			}
		}

		if (isMenuOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		} else {
			document.removeEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isMenuOpen]);

	const handleOpenCreateCategory = () => {
		setIsCreateCategoryModalOpen(true);
		setIsMenuOpen(false);
	};

	const handleOpenCreateGroup = () => {
		setIsCreateGroupModalOpen(true);
		setIsMenuOpen(false);
	};

	const itemVariants = {
		initial: { opacity: 0, y: 20, scale: 0.9 },
		animate: {
			opacity: 1,
			y: 0,
			scale: 1,
			transition: { type: "spring", stiffness: 400, damping: 20 },
		},
		exit: {
			opacity: 0,
			y: 20,
			scale: 0.9,
			transition: { type: "spring", stiffness: 400, damping: 20 },
		},
	};

	return (
		<MainSkeletonContainer
			pageTitle="Categorias"
			iconTitle={categoryItem?.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{ href: "/produtos", label: "Produtos", icon: PRODUCT_CONFIG.Icon },
			]}
			currentBreadcrumb={{
				href: `/produtos/categorias`,
				label: `Categorias`,
				icon: categoryItem?.Icon,
			}}
		>
			<FiltersSection methods={methods.methods} />
			<TableCategories
				filter={methods.debouncedFilter}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
			<div ref={menuRef} className="md:hidden fixed bottom-24 right-4 flex flex-col items-end gap-3 z-[60]">
				<AnimatePresence>
					{isMenuOpen && (
						<motion.div
							className="flex flex-col items-end gap-3 mb-2"
							initial="initial"
							animate="animate"
							exit="exit"
							variants={{
								animate: { transition: { staggerChildren: 0.1, staggerDirection: -1 } },
								exit: { transition: { staggerChildren: 0.05 } },
							}}
						>
							<motion.button
								variants={itemVariants}
								onClick={handleOpenCreateGroup}
								className="flex items-center gap-2 bg-blue-600 text-white h-[48px] px-4 py-2 rounded-[15px] shadow-xl hover:bg-blue-700 transition-colors"
							>
								<Users size={18} />
								Grupo
							</motion.button>
							<motion.button
								variants={itemVariants}
								onClick={handleOpenCreateCategory}
								className="flex items-center gap-2 bg-mainColor text-white h-[48px] px-4 py-2 rounded-[15px] shadow-xl hover:bg-mainColor/90 transition-colors"
							>
								<IoAdd size={18} />
								Categoria
							</motion.button>
						</motion.div>
					)}
				</AnimatePresence>

				<motion.button
					onClick={() => setIsMenuOpen(prev => !prev)}
					className="bg-mainColor text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl border-4 border-white hover:scale-105 active:scale-95 transition-all duration-200"
					whileHover={{ rotate: isMenuOpen ? 45 : 0 }}
					whileTap={{ scale: 0.9 }}
					aria-label="Menu de adição"
				>
					<IoAdd size={28} className={`transition-transform duration-200 ${isMenuOpen ? "rotate-45" : ""}`} />
				</motion.button>
			</div>

			{/* Modais */}
			<CreateCategoryModal isOpen={isCreateCategoryModalOpen} onClose={() => setIsCreateCategoryModalOpen(false)} />
			<CreateGroupModal isOpen={isCreateGroupModalOpen} onClose={() => setIsCreateGroupModalOpen(false)} />
		</MainSkeletonContainer>
	);
};

export default CategoryPage;
