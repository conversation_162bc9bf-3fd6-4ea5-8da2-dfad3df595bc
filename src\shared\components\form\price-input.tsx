import React from "react";
import { Control, FieldPath, FieldValues } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { Label } from "../ui/label";
import { usePriceField } from "../../hooks/use-price-field";

interface PriceInputProps<T extends FieldValues> {
	name: FieldPath<T>;
	control: Control<T>;
	label: string;
	placeholder?: string;
	required?: boolean;
	readOnly?: boolean;
	className?: string;
	labelClassName?: string;
	rules?: object;
	defaultValue?: number;
}

export function PriceInput<T extends FieldValues>({
	name,
	control,
	label,
	placeholder = "R$ 0,00",
	required = false,
	readOnly = false,
	className = "",
	labelClassName = "text-xs",
	rules,
	defaultValue,
}: PriceInputProps<T>) {
	const { value, onChange, error, ...fieldProps } = usePriceField({
		name,
		control,
		rules: {
			required: required ? `${label} é obrigatório` : false,
			min: {
				value: 0.01,
				message: `${label} deve ser maior que zero`,
			},
			...rules,
		},
		defaultValue,
	});

	const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
		<span className="flex items-center">
			{children}
			{required && <span className="text-red-500 ml-1">*</span>}
		</span>
	);

	return (
		<div className="w-full">
			<Label className={labelClassName}>
				<RequiredLabel>{label}</RequiredLabel>
			</Label>
			<NumericFormat
				{...fieldProps}
				value={value}
				onValueChange={values => onChange(values.formattedValue, values.floatValue)}
				thousandSeparator="."
				decimalSeparator=","
				prefix="R$ "
				decimalScale={2}
				fixedDecimalScale
				placeholder={placeholder}
				readOnly={readOnly}
				className={`text-sm mt-1 w-full px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
					readOnly ? "bg-gray-100 cursor-not-allowed text-gray-500" : ""
				} ${error ? "border-red-500" : "border-gray-300"} ${className}`}
			/>
			{error && <span className="text-red-600 text-xs mt-1 block">{error.message}</span>}
		</div>
	);
}
