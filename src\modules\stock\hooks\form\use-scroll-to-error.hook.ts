import { useRef, useCallback } from "react";
import { FieldErrors, FieldValues } from "react-hook-form";

interface IUseScrollToErrorHook<T extends FieldValues> {
	errors: FieldErrors<T>;
	arrayFieldName: keyof T;
}

export function useScrollToError<T extends FieldValues>({ errors, arrayFieldName }: IUseScrollToErrorHook<T>) {
	const itemRefs = useRef<(HTMLElement | null)[]>([]);

	const findFirstErrorIndex = useCallback((): number => {
		const arrayErrors = errors[arrayFieldName] as FieldErrors[] | undefined;
		if (!arrayErrors) return -1;

		for (let i = 0; i < arrayErrors.length; i++) {
			if (arrayErrors[i]) {
				return i;
			}
		}
		return -1;
	}, [errors, arrayFieldName]);

	const scrollToFirstError = useCallback(() => {
		const firstErrorIndex = findFirstErrorIndex();
		if (firstErrorIndex !== -1 && itemRefs.current[firstErrorIndex]) {
			itemRefs.current[firstErrorIndex]?.scrollIntoView({
				behavior: "smooth",
				block: "center",
			});
		}
	}, [findFirstErrorIndex]);

	const setItemRef = useCallback(
		(index: number) => (el: HTMLElement | null) => {
			itemRefs.current[index] = el;
		},
		[]
	);

	return {
		scrollToFirstError,
		setItemRef,
		itemRefs,
	};
}
