import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { AnimatePresence, motion } from "framer-motion";
import { Home, ShoppingCart } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { OrderDetailsModal } from "../components/order-details-modal";
import { OrderFilters } from "../components/orders-filters";
import { OrdersListContent } from "../components/orders-list-content";
import { OrdersSummaryCards } from "../components/orders-summary-cards";
import { useOrderDetailsModal } from "../hooks/use-order-details-modal.hook";
import { useOrderModal } from "../hooks/use-order-modal.hook";
import { OrderFiltersForm, useOrdersList } from "../hooks/use-orders-list.hook";

export function OrdersListPage() {
	const { openModal } = useOrderModal();
	const {
		isOpen: isDetailsModalOpen,
		orderId: selectedOrderId,
		openModal: openDetailsModal,
		closeModal: closeDetailsModal,
	} = useOrderDetailsModal();

	const methods = useForm<OrderFiltersForm>({
		defaultValues: {
			orderId: "",
			customer: "",
			orderStatus: "all",
		},
	});

	const pagination = usePagination(50);
	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = pagination;

	const { orders, isLoading, queryError, totalPages, tableKey } = useOrdersList({
		methods,
		pagination,
	});

	const [error, setError] = useState<string | null>(null);
	const filters = methods.watch();

	useEffect(() => {
		setError(null);
	}, [page, itemsPerPage, filters]);

	useEffect(() => {
		if (!isLoading && !orders && queryError) {
			setError("Erro ao carregar pedidos");
		}
	}, [isLoading, orders, queryError]);

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<div className="flex justify-center items-center py-10 animate-pulse">
					<span className="text-gray-400 text-lg">Carregando pedidos...</span>
				</div>
			);
		}
		if (error || queryError) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-red-500 text-lg font-semibold mb-2">Erro ao carregar pedidos</span>
					<span className="text-gray-500 text-sm">Tente novamente mais tarde.</span>
				</div>
			);
		}
		if (!orders || orders.length === 0) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-gray-500 text-lg font-semibold mb-2">Nenhum pedido encontrado</span>
					<span className="text-gray-400 text-sm">Ajuste os filtros ou tente novamente.</span>
				</div>
			);
		}
		return (
			<OrdersListContent orders={orders} isLoading={isLoading} queryError={queryError} tableKey={tableKey} onOrderClick={openDetailsModal} />
		);
	};

	return (
		<MainSkeletonContainer
			pageTitle="Lista de Pedidos"
			iconTitle={ShoppingCart}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{ href: "/pedidos", label: "Pedidos", icon: ShoppingCart }}
		>
			<OrdersSummaryCards />
			<OrderFilters methods={methods} onNewOrder={openModal} />
			<CardLMPContainer
				title="Lista de Pedidos"
				description="Gerencie todos os pedidos do sistema"
				icon={<ShoppingCart size={22} className="text-gray-500" />}
			>
				<AnimatePresence mode="wait">
					<motion.div
						key={tableKey}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2 }}
					>
						{renderTableContent()}
					</motion.div>
				</AnimatePresence>
				<Pagination
					totalPages={totalPages}
					itemsPerPage={itemsPerPage}
					page={page}
					onPageChange={handlePageChange}
					onItemsPerPageChange={handleItemsPerPageChange}
				/>

				<button
					onClick={openModal}
					className="md:hidden fixed bottom-6 right-6 z-50 bg-mainColor text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg"
				>
					<IoAdd size={24} />
				</button>
			</CardLMPContainer>

			<OrderDetailsModal isOpen={isDetailsModalOpen} onClose={closeDetailsModal} orderId={selectedOrderId} />
		</MainSkeletonContainer>
	);
}
