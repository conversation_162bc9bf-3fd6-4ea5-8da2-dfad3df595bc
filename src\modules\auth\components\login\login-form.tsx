import { But<PERSON> } from "@/shared/components/ui/button";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import { useState } from "react";

import { useAuthProvider } from "../../hooks/auth-provider.hook";
import { useLoginValidator } from "../../validators/login.validators";

export const LoginForm = () => {
	const methods = useLoginValidator();
	const { loginSubmit, isLoading } = useAuthProvider();
	const [showPassword, setShowPassword] = useState(false);
	return (
		<form onSubmit={methods.handleSubmit(loginSubmit)} className="space-y-5 sm:space-y-6">
			{/* Email Field */}
			<div className="space-y-2">
				<Label htmlFor="email" className="text-sm font-medium text-gray-700 flex items-center gap-2">
					<Mail size={16} className="text-[#0197B2]" />
					Usuário
				</Label>
				<div className="relative">
					<Input
						{...methods.register("email")}
						type="email"
						placeholder="<EMAIL>"
						className={`pl-11 sm:pl-12 h-11 sm:h-12 text-base border-2 rounded-xl transition-all duration-200 ${
							methods.formState.errors.email
								? "border-red-400 focus:border-red-500 focus:ring-red-200"
								: "border-gray-200 focus:border-[#0197B2] focus:ring-[#0197B2]/20"
						}`}
					/>
					<Mail size={18} className="absolute left-3.5 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 sm:w-5 sm:h-5" />
				</div>
				{methods.formState.errors.email && (
					<p className="text-red-500 text-sm flex items-center gap-1 mt-1">
						<span className="w-1 h-1 bg-red-500 rounded-full"></span>
						{methods.formState.errors.email.message}
					</p>
				)}
			</div>
			<div className="space-y-2">
				<Label htmlFor="password" className="text-sm font-medium text-gray-700 flex items-center gap-2">
					<Lock size={16} className="text-[#0197B2]" />
					Senha
				</Label>{" "}
				<div className="relative">
					<Input
						{...methods.register("password")}
						type={showPassword ? "text" : "password"}
						placeholder="Digite sua senha"
						className={`pl-11 sm:pl-12 pr-11 sm:pr-12 h-11 sm:h-12 text-base border-2 rounded-xl transition-all duration-200 ${
							methods.formState.errors.password
								? "border-red-400 focus:border-red-500 focus:ring-red-200"
								: "border-gray-200 focus:border-[#0197B2] focus:ring-[#0197B2]/20"
						}`}
					/>
					<Lock size={18} className="absolute left-3.5 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 sm:w-5 sm:h-5" />
					<button
						type="button"
						onClick={() => setShowPassword(!showPassword)}
						className="absolute right-3.5 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
					>
						{showPassword ? <EyeOff size={18} className="sm:w-5 sm:h-5" /> : <Eye size={18} className="sm:w-5 sm:h-5" />}
					</button>
				</div>
				{methods.formState.errors.password && (
					<p className="text-red-500 text-sm flex items-center gap-1 mt-1">
						<span className="w-1 h-1 bg-red-500 rounded-full"></span>
						{methods.formState.errors.password.message}
					</p>
				)}
			</div>{" "}
			<div className="flex items-center py-2">
				<div className="flex items-center space-x-3">
					<Checkbox
						id="remember"
						className="border-2 border-gray-300 data-[state=checked]:bg-[#0197B2] data-[state=checked]:border-[#0197B2]"
					/>
					<Label htmlFor="remember" className="text-sm text-gray-600 cursor-pointer hover:text-gray-800 transition-colors">
						Manter conectado
					</Label>
				</div>
			</div>
			<Button
				type="submit"
				variant="primary"
				className="w-full h-11 sm:h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-[#0197B2] to-[#016d85] hover:from-[#016d85] hover:to-[#0197B2] transform hover:scale-[1.02] transition-all duration-200 shadow-lg hover:shadow-xl"
				disabled={isLoading}
			>
				{isLoading ? (
					<div className="flex items-center gap-2">
						<div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
						Entrando...
					</div>
				) : (
					"Entrar"
				)}
			</Button>
		</form>
	);
};
