import { IPaginatedResponse } from "@/shared/types/requests/requests.type";

export interface IFindAllBillsParamsDto {
	page: number;
	limit: number;
	type?: number;
	description?: string;
	dueDate?: string;
	paymentDate?: string;
	personId?: number;
	accountId?: number;
}

export interface IBillsDto extends IPaginatedResponse<IBillDto> {
	total: number;
}

export interface IBillDto {
	id: number;
	type: string;
	description: string;
	value: string;
	dueDate: string;
	paymentDate: string;
	person: string;
	// status: string;
	account: string;
	createdAt: string;
}
