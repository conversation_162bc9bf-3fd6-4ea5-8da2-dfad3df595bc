import { Upload } from "lucide-react";
import React, { useCallback } from "react";
import { useUploadXml } from "../../hooks/xml/upload-xml.hook";
import { ICreateStock } from "../../validators/create-stock.validator";

export const XmlUploadSection: React.FC<{ reset: (data: ICreateStock) => void; onFileSelected: () => void; showForm: boolean }> = ({
	reset,
	onFileSelected,
	showForm,
}) => {
	const { uploadXml, isLoading, error } = useUploadXml({ reset });

	const handleFileChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const file = e.target.files?.[0];
			if (file) {
				onFileSelected();
				uploadXml(file);
			}
		},
		[uploadXml, onFileSelected]
	);

	return (
		<section>
			{!showForm ? (
				<>
					{" "}
					<div
						className="
		  relative 
		  border-2 border-dashed border-gray-200 
		  rounded-lg p-6 
		  text-center 
		  hover:cursor-pointer 
		  hover:border-mainColor 
		  transition-all
		"
					>
						<Upload className="mx-auto mb-4 text-gray-600" size={48} />
						<p className="text-lg text-gray-700 font-semibold">Arraste e solte seu arquivo XML aqui</p>
						<p className="text-sm text-gray-500 mb-4">ou clique para selecionar</p>

						<input
							type="file"
							accept=".xml"
							onChange={handleFileChange}
							className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
						/>
					</div>
					{isLoading && <p className="mt-2 text-blue-600">Enviando XML...</p>}
					{error && <p className="mt-2 text-red-600">Ocorreu um erro: {(error as Error).message}</p>}
				</>
			) : (
				<></>
			)}
		</section>
	);
};
