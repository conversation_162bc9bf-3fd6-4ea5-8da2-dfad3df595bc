import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, ChevronsUpDown, Search, Hash } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { useNcmSearch } from "../hooks/use-ncm-search.hook";
import { INcmSearchResult } from "../types/ncm.types";
import { ncmSearchUtil } from "../utils/ncm-search.util";
import "../utils/ncm-preloader";

interface INcmSelectProps {
	value?: string;
	onChange: (value: string) => void;
	error?: string;
	placeholder?: string;
	disabled?: boolean;
}

export const NcmSelect: React.FC<INcmSelectProps> = ({ value, onChange, error, placeholder = "Selecione um NCM", disabled = false }) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");

	const { results, isLoading, isDataLoaded } = useNcmSearch({
		searchTerm,
		limit: 50,
	});

	const selectedNcm = useMemo(() => {
		if (!value) return null;

		if (isDataLoaded) {
			const foundNcm = ncmSearchUtil.findExactNcm(value.replace(/\./g, ""));
			if (foundNcm) {
				return foundNcm;
			}
			return {
				codigo: value,
				descricao: "NCM não encontrado",
				displayText: `${value} (NCM não encontrado)`,
				notFound: true,
			};
		}

		return {
			codigo: value,
			descricao: "",
			displayText: value,
		};
	}, [value, isDataLoaded]);

	const getButtonText = () => {
		if (selectedNcm && value) {
			return selectedNcm.displayText || value;
		}
		if (value && !isDataLoaded) {
			return value;
		}
		if (isLoading && !value) {
			return "Carregando dados NCM...";
		}
		return placeholder;
	};

	const getButtonTextColor = useCallback(() => {
		if (selectedNcm?.notFound) {
			return "text-red-600";
		}
		return "text-gray-900";
	}, [selectedNcm?.notFound]);

	const handleSelect = useCallback(
		(ncmResult: INcmSearchResult) => {
			onChange(ncmResult.codigo);
			setOpen(false);
			setSearchTerm("");
		},
		[onChange]
	);

	return (
		<div className="relative">
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<motion.button
						type="button"
						whileHover={{ scale: disabled ? 1 : 1.01 }}
						whileTap={{ scale: disabled ? 1 : 0.99 }}
						transition={{ type: "spring", stiffness: 400, damping: 17 }}
						className={cn(
							"w-full px-4 py-2.5 text-sm transition-all duration-200",
							"border rounded-lg bg-white/50",
							"focus:outline-none focus:ring-2 focus:ring-mainColor/50",
							"flex items-center justify-between",
							disabled && "bg-gray-100 cursor-not-allowed text-gray-500",
							error ? "border-red-300 focus:border-red-500 focus:ring-red-500/30" : "border-gray-200 hover:border-mainColor/40"
						)}
						disabled={disabled || isLoading || !isDataLoaded}
					>
						<div className="flex items-center gap-2 truncate">
							<Hash size={16} className={`flex-shrink-0 ${selectedNcm?.notFound ? "text-red-400" : "text-gray-400"}`} />
							<span className={`truncate ${getButtonTextColor()}`}>{getButtonText()}</span>
						</div>
						<motion.div animate={{ rotate: open ? 180 : 0 }} transition={{ duration: 0.2 }}>
							<ChevronsUpDown size={16} className="text-gray-500 flex-shrink-0" />
						</motion.div>
					</motion.button>
				</PopoverTrigger>
				<PopoverContent className="w-[--radix-popover-trigger-width] z-[100000] p-0" align="start">
					<div className="flex flex-col h-full">
						<div className="relative">
							<span className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								<Search size={16} />
							</span>
							<input
								placeholder="Buscar por código ou descrição..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="h-9 pl-8 pr-2 text-sm border-b border-gray-200 focus:outline-none focus:ring-0 w-full"
								autoFocus
							/>
						</div>
						<div className="overflow-y-auto max-h-[300px]">
							{!searchTerm.trim() && <div className="py-3 px-3 text-sm text-gray-500 text-center">Digite para buscar NCM...</div>}
							{searchTerm.trim() && isLoading && <div className="py-2 px-2 text-sm text-gray-500">Buscando...</div>}
							{searchTerm.trim() && !isLoading && results.length === 0 && (
								<div className="py-3 px-3 text-sm text-gray-500 text-center">Nenhum NCM encontrado para "{searchTerm}"</div>
							)}
							<AnimatePresence>
								{results.map(ncmResult => (
									<motion.li
										key={ncmResult.codigo}
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: -10 }}
										transition={{ duration: 0.2 }}
										className="flex items-center justify-between py-2 px-3 cursor-pointer hover:bg-mainColor/5 border-b border-gray-50 last:border-b-0"
										onClick={() => handleSelect(ncmResult)}
									>
										<div className="flex flex-col flex-1 min-w-0">
											<span className="font-medium text-sm text-gray-900 truncate">{ncmResult.codigo}</span>
											<span className="text-xs text-gray-500 truncate">{ncmResult.descricao}</span>
										</div>
										{ncmResult.codigo === value && <Check size={16} className="text-mainColor flex-shrink-0 ml-2" />}
									</motion.li>
								))}
							</AnimatePresence>
						</div>
						{searchTerm.trim() && results.length > 0 && (
							<div className="border-t border-gray-100 px-3 py-2 text-xs text-gray-500 text-center">
								{results.length} resultado{results.length !== 1 ? "s" : ""} encontrado{results.length !== 1 ? "s" : ""}
							</div>
						)}
					</div>
				</PopoverContent>
			</Popover>
			<AnimatePresence>
				{error && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						className="absolute mt-1 text-xs text-red-500 flex items-center gap-1"
					>
						<AlertCircle size={12} />
						<span className="font-medium">{error}</span>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};
