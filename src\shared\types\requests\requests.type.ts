interface IResultResponse<G> {
	success: boolean;
	data: G;
	status: number;
}
export interface ISuccessResponse<T> extends IResultResponse<T> {
	success: true;
}

export interface IErrorResponse extends IResultResponse<ErrorData> {
	success: false;
}

export type ErrorData = {
	message: string;
	method?: string;
	url?: string;
};

export type ApiResponse<T> = ISuccessResponse<T> | IErrorResponse;

export interface IPaginationData {
	total: number;
	totalPages?: number;
	currentPage?: number;
	itemsPerPage?: number;
}

export interface IPaginatedResponse<T> {
	data: T[];
	pagination: IPaginationData;
}
