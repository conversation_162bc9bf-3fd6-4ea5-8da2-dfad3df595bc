import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Filter, RefreshCw, Search, Tag } from "lucide-react";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { OrderStatusEnum } from "../enums/order-status.enum";

interface OrderFiltersForm {
	orderId: string;
	customer: string;
	orderStatus: string;
}

interface OrderFiltersProps {
	methods: UseFormReturn<OrderFiltersForm>;
	onNewOrder?: () => void;
}

const getStatusLabel = (status: OrderStatusEnum): string => {
	const statusLabels = {
		[OrderStatusEnum.Open]: "Aberto",
		[OrderStatusEnum.InProcessing]: "Em Processamento",
		[OrderStatusEnum.Finished]: "Finalizado",
		[OrderStatusEnum.Canceled]: "Cancelado",
	};
	return statusLabels[status];
};

export function OrderFilters({ methods, onNewOrder }: OrderFiltersProps) {
	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<Button
					className="hidden md:flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
					onClick={onNewOrder}
				>
					<IoAdd size={18} />
					Novo Pedido
				</Button>
			}
		>
			<FormProvider {...methods}>
				<form className="flex flex-col gap-3 md:flex-row md:items-end justify-between w-full">
					<div className="flex-1 min-w-0 md:w-[30%]">
						<label htmlFor="orderId" className="block text-sm font-medium text-gray-600 mb-1">
							ID do Pedido
						</label>
						<div className="relative">
							<Input
								id="orderId"
								type="text"
								placeholder="Buscar por ID..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("orderId")}
							/>
							<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="flex-1 min-w-0 md:w-[30%]">
						<label htmlFor="customer" className="block text-sm font-medium text-gray-600 mb-1">
							Cliente
						</label>
						<div className="relative">
							<Input
								id="customer"
								type="text"
								placeholder="Buscar por cliente..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("customer")}
							/>
							<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="flex-1 min-w-0 md:w-[30%]">
						<label htmlFor="orderStatus" className="block text-sm font-medium text-gray-600 mb-1">
							Status do Pedido
						</label>
						<div className="relative">
							<Select value={methods.watch("orderStatus") || undefined} onValueChange={value => methods.setValue("orderStatus", value)}>
								<SelectTrigger id="orderStatus" className="w-full h-[45px] rounded-[10px] pl-10">
									<SelectValue placeholder="Todos os status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">Todos os status</SelectItem>
									{Object.values(OrderStatusEnum)
										.filter(value => typeof value === "number")
										.map(status => (
											<SelectItem key={status} value={status.toString()}>
												{getStatusLabel(status as OrderStatusEnum)}
											</SelectItem>
										))}
								</SelectContent>
							</Select>
							<Tag className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="md:w-auto">
						<Button
							type="button"
							variant="outline"
							className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2 whitespace-nowrap"
							onClick={() => {
								methods.reset();
								methods.setValue("orderStatus", "all");
							}}
						>
							<RefreshCw size={18} className="text-mainColor" />
							<span>Resetar Filtros</span>
						</Button>
					</div>
				</form>
			</FormProvider>
		</CardLMPContainer>
	);
}
