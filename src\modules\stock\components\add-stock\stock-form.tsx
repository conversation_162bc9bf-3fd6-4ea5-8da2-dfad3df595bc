// StockForm.tsx
import React, { useCallback, useRef } from "react";
import { FormProvider, UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import { useCreateStockMutation } from "../../hooks/stock/create-stock-mutation.hook";
import { CreateStockDtoMapper } from "../../lib/form-to-create-stock-dto";
import { ICreateStock } from "../../validators/create-stock.validator";
import { InventoryItemsList, InventoryItemsListRef } from "./inventory-item-list";
import { InvoiceForm } from "./invoice-form";

export const StockForm: React.FC<{
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">;
	methodsForm: UseFormReturn<ICreateStock>;
	formMode: "xml" | "manual";
	firstInputRef: React.RefObject<HTMLInputElement>;
	onSuccess?: () => void;
}> = ({ methodsForm, inventoryFieldArray, formMode, firstInputRef, onSuccess }) => {
	const { createStock } = useCreateStockMutation({
		reset: methodsForm.reset,
		inventoryFieldArray,
		onSuccess,
	});
	const inventoryListRef = useRef<InventoryItemsListRef>(null);

	const onSubmit = async (data: ICreateStock) => {
		toast.dismiss();
		toast.loading("Criando estoque...");

		const create = CreateStockDtoMapper.map(data);
		console.log(create);
		createStock(create);
	};

	const onInvalidSubmit = () => {
		setTimeout(() => {
			inventoryListRef.current?.scrollToFirstError();
		}, 100);
	};

	const handleFormKeyDown = useCallback((e: React.KeyboardEvent<HTMLFormElement>) => {
		if (e.key === "Enter") {
			const target = e.target as HTMLElement;
			if (target.tagName === "INPUT" && target.getAttribute("name")?.includes("barcode")) {
				e.preventDefault();
				const form = e.currentTarget;
				const barcodeInputs = Array.from(form.querySelectorAll('input[name*="barcode"]')) as HTMLInputElement[];
				const currentIndex = barcodeInputs.findIndex(input => input === target);
				if (currentIndex !== -1 && currentIndex < barcodeInputs.length - 1) {
					barcodeInputs[currentIndex + 1].focus();
				}
			} else {
				e.preventDefault();
			}
		}
	}, []);

	return (
		<section className="">
			<FormProvider {...methodsForm}>
				<form onSubmit={methodsForm.handleSubmit(onSubmit, onInvalidSubmit)} onKeyDown={handleFormKeyDown}>
					<InvoiceForm methodsForm={methodsForm} formMode={formMode} firstInputRef={firstInputRef} />
					<InventoryItemsList ref={inventoryListRef} methodsForm={methodsForm} inventoryFieldArray={inventoryFieldArray} />

					<button
						type="submit"
						className="bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
					>
						Adicionar Estoque
					</button>
				</form>
			</FormProvider>
		</section>
	);
};
