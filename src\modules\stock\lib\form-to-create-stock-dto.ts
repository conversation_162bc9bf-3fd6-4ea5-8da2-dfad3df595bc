import { z } from "zod";
import { ICreateStockDto, IProductDetails, IStockMovement, ISuplierCreateStockDTO } from "../dtos/create-stock.dto";
import { createStockDtoSchema, ICreateStock } from "../validators/create-stock.validator";
import { convertDateToISOWithTimezone } from "@/shared/utils/date-converter";
import { NcmFormatterUtil } from "../utils/ncm-formatter.util";

export class CreateStockDtoMapper {
	public static map(form: ICreateStock): ICreateStockDto {
		return {
			invoice: CreateStockDtoMapper.mapInvoice(form.invoice),
			inventories: form.inventories.map(inv => CreateStockDtoMapper.mapInventory(inv)),
		};
	}
	private static mapInvoice(invoice: z.infer<typeof createStockDtoSchema>["invoice"]): ISuplierCreateStockDTO {
		console.log(invoice);

		const convertedDate = invoice.issueDate ? convertDateToISOWithTimezone(invoice.issueDate.trim()) : undefined;

		const obj = {
			key: invoice.key?.trim(),
			issueDate: convertedDate,
			supplierId: invoice.supplier?.id || 0,
		};

		console.log("Object before removeUndefined:", obj);

		const result = CreateStockDtoMapper.removeUndefined(obj) as ISuplierCreateStockDTO;
		console.log("Object after removeUndefined:", result);
		console.log("=== END DEBUG ===");

		return result;
	}

	private static mapInventory(inventory: z.infer<typeof createStockDtoSchema>["inventories"][number]): ICreateStockDto["inventories"][number] {
		const obj = {
			expirationDate:
				inventory.expirationDate?.trim() === "" || !inventory.expirationDate
					? undefined
					: convertDateToISOWithTimezone(inventory.expirationDate),
			id: inventory.id,
			stockMovement: CreateStockDtoMapper.mapStockMovement(inventory.stockMovement),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as ICreateStockDto["inventories"][number];
	}

	private static mapStockMovement(stockMovement: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]): IStockMovement {
		const obj = {
			quantity: stockMovement.quantity !== undefined ? Number(stockMovement.quantity) : undefined,
			description: stockMovement.description?.trim() || undefined,
			product: CreateStockDtoMapper.mapProduct(stockMovement.product),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as IStockMovement;
	}

	private static mapProduct(product: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]["product"]): IProductDetails {
		if (product.id !== undefined && product.id !== null) {
			return { id: product.id } as IProductDetails;
		}

		const convertedPrice = product.price ?? 0;
		const convertedCostPrice = product.costPrice ?? 0;

		const hasPackageData =
			product.package?.id ||
			(product.package?.name?.trim() &&
				product.package?.barcode?.trim() &&
				product.package?.code?.trim() &&
				product.package?.quantityPerPackage);

		const mappedPackage = hasPackageData
			? product.package?.id !== undefined && product.package?.id !== null
				? {
						id: product.package.id,
						name: product.package.name?.trim() ?? "",
						barcode: product.package.barcode?.trim() ?? "",
						code: product.package.code?.trim() ?? "",
						quantityPerPackage: product.package.quantityPerPackage ? Math.floor(Number(product.package.quantityPerPackage)) : 0,
					}
				: {
						name: product.package?.name?.trim() ?? "",
						barcode: product.package?.barcode?.trim() ?? "",
						code: product.package?.code?.trim() ?? "",
						quantityPerPackage: product.package?.quantityPerPackage ? Math.floor(Number(product.package.quantityPerPackage)) : 0,
					}
			: undefined;
		const obj: Partial<IProductDetails> = {
			name: product.name?.trim(),
			barcode: product.barcode?.trim(),
			supplierCode: product.supplierCode?.trim(),
			price: convertedPrice,
			ncm: product.ncm ? NcmFormatterUtil.cleanNcm(product.ncm) : undefined,
			description: product.description?.trim() || undefined,
			categoryId: product.categoryId,
			costPrice: convertedCostPrice,
		};

		if (mappedPackage) {
			obj.package = mappedPackage;
		}

		return CreateStockDtoMapper.removeUndefined(obj) as IProductDetails;
	}

	private static removeUndefined<T extends object>(obj: T): Partial<T> {
		const result: Partial<T> = {};

		for (const [key, value] of Object.entries(obj)) {
			if (value === undefined || value === null || value === "") {
				continue;
			}

			if (typeof value === "object" && value !== null && !Array.isArray(value)) {
				const cleanedValue = CreateStockDtoMapper.removeUndefined(value);
				if (Object.keys(cleanedValue).length > 0) {
					result[key as keyof T] = cleanedValue as T[keyof T];
				}
			} else {
				result[key as keyof T] = value as T[keyof T];
			}
		}

		return result as Partial<T>;
	}
}
