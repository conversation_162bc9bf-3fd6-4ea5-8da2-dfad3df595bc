import { Calendar } from "@/shared/components/ui/calendar";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/ui/form";
import { Input } from "@/shared/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { format, isValid, parse, setHours, setMinutes } from "date-fns";
import { ptBR } from "date-fns/locale";
import React, { useState, useMemo } from "react";
import { ControllerRenderProps, FieldError, FieldErrors, FieldPath, FieldValues, UseFormReturn } from "react-hook-form";
import { FaCalendarAlt, FaFileInvoice, FaInfoCircle, FaLock, FaUser, FaChevronDown, FaSearch } from "react-icons/fa";
import { ICreateStock } from "../../validators/create-stock.validator";
import { PersonClassificationEnum } from "@/modules/person/enums/person-classification.enum";
import { usePersonFindAll } from "@/modules/person/hooks/find-all.hook";

interface InvoiceFormProps {
	methodsForm: UseFormReturn<ICreateStock>;
	formMode: "xml" | "manual";
	firstInputRef: React.RefObject<HTMLInputElement>;
}

const parseDateTime = (dateString: string): Date | null => {
	let parsed = parse(dateString, "yyyy-MM-dd HH:mm:ss", new Date());
	if (!isValid(parsed)) parsed = parse(dateString, "yyyy-MM-dd HH:mm", new Date());
	if (!isValid(parsed)) parsed = parse(dateString, "yyyy-MM-dd", new Date());
	if (!isValid(parsed)) parsed = parse(dateString, "dd/MM/yyyy HH:mm:ss", new Date());
	if (!isValid(parsed)) parsed = parse(dateString, "dd/MM/yyyy HH:mm", new Date());
	if (!isValid(parsed)) parsed = parse(dateString, "dd/MM/yyyy", new Date());
	return isValid(parsed) ? parsed : null;
};

interface DateTimeFieldProps {
	field: ControllerRenderProps<ICreateStock, "invoice.issueDate">;
	formMode: "xml" | "manual" | undefined;
	errors: FieldErrors<ICreateStock>;
}

const DateTimeField: React.FC<DateTimeFieldProps> = ({ field, formMode, errors }) => {
	const selectedDate = field.value ? parseDateTime(field.value) : null;
	const [timeString, setTimeString] = useState(selectedDate ? format(selectedDate, "HH:mm") : "");

	const handleSelectDate = (newDate: Date | undefined) => {
		if (!newDate) return;
		const [hh, mm] = timeString.split(":");
		const hours = parseInt(hh) || 0;
		const mins = parseInt(mm) || 0;
		const finalDate = setMinutes(setHours(newDate, hours), mins);
		field.onChange(format(finalDate, "yyyy-MM-dd HH:mm"));
	};

	const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const newValue = e.target.value;
		setTimeString(newValue);

		let baseDate = selectedDate || new Date();
		if (!selectedDate) baseDate = parse("01/01/2000", "dd/MM/yyyy", new Date());

		const [hh, mm] = newValue.split(":");
		const hours = parseInt(hh) || 0;
		const mins = parseInt(mm) || 0;

		const finalDate = setMinutes(setHours(baseDate, hours), mins);
		field.onChange(format(finalDate, "yyyy-MM-dd HH:mm"));
	};

	return (
		<FormItem>
			<FormLabel>Data/Hora de Emissão:</FormLabel>
			<div className="relative">
				<Popover>
					<PopoverTrigger asChild>
						<div className="relative">
							<Input
								name={field.name}
								value={selectedDate ? format(selectedDate, "dd/MM/yyyy HH:mm") : ""}
								onBlur={field.onBlur}
								disabled={formMode === "xml"}
								readOnly
								placeholder={formMode === "xml" ? "Data/hora do XML" : "Clique para selecionar data e hora"}
								className={`
									h-[45px] pl-10 w-full
									${
										formMode === "xml"
											? "bg-gray-100 text-gray-500 cursor-not-allowed"
											: "bg-white text-gray-900 border-gray-200 hover:border-gray-300 focus:border-mainColor focus:ring-1 focus:ring-mainColor cursor-pointer"
									}
								`}
							/>
							<FaCalendarAlt className="absolute top-1/2 left-4 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
						</div>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-3" align="start">
						<Calendar
							mode="single"
							locale={ptBR}
							selected={selectedDate || undefined}
							onSelect={handleSelectDate}
							disabled={(date: Date) => date > new Date() || date < new Date("1900-01-01")}
							initialFocus
						/>
						<div className="mt-2 flex items-center space-x-2">
							<label className="text-sm text-gray-600">Hora:</label>
							<Input
								type="time"
								value={timeString}
								onChange={handleTimeChange}
								className="h-[35px] w-[150px] bg-white text-gray-900 border-gray-200 hover:border-gray-300 focus:border-mainColor focus:ring-1 focus:ring-mainColor"
							/>
						</div>
					</PopoverContent>
				</Popover>
			</div>
			{errors.invoice?.issueDate && <FormMessage>{errors.invoice.issueDate.message}</FormMessage>}
		</FormItem>
	);
};

interface Supplier {
	id: number;
	name: string;
}

interface SupplierSelectFieldProps {
	field: ControllerRenderProps<ICreateStock, "invoice.supplier.name">;
	formMode: "xml" | "manual";
	errors?: FieldError;
	onSupplierChange: (supplier: Supplier | null) => void;
}

const SupplierSelectField: React.FC<SupplierSelectFieldProps> = ({ field, formMode, errors, onSupplierChange }) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);

	const { data, isLoading } = usePersonFindAll({
		page: 1,
		limit: 50,
		search: searchTerm,
		classification: PersonClassificationEnum.SUPPLIER,
	});

	const suppliers = useMemo(() => {
		if (!data?.success) return [];
		return data.data.data as Supplier[];
	}, [data]);

	const handleSupplierSelect = (supplier: Supplier) => {
		setSelectedSupplier(supplier);
		field.onChange(supplier.name);
		onSupplierChange(supplier);
		setOpen(false);
		setSearchTerm("");
	};

	const displayValue = selectedSupplier?.name || field.value || "";
	const placeholder = formMode === "xml" ? "Nome do fornecedor do XML" : "Clique para selecionar fornecedor";

	return (
		<FormItem>
			<FormLabel>Fornecedor:</FormLabel>
			<div className="relative">
				<Popover open={open} onOpenChange={setOpen}>
					<PopoverTrigger asChild>
						<div className="relative">
							<Input
								value={displayValue}
								disabled={formMode === "xml"}
								readOnly
								placeholder={placeholder}
								className={`
									h-[45px] pl-10 pr-10 w-full cursor-pointer
									${
										formMode === "xml"
											? "bg-gray-100 text-gray-500 cursor-not-allowed"
											: "bg-white text-gray-900 border-gray-200 hover:border-gray-300 focus:border-mainColor focus:ring-1 focus:ring-mainColor"
									}
								`}
							/>
							<FaUser className="absolute top-1/2 left-4 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
							{formMode !== "xml" && (
								<FaChevronDown className="absolute top-1/2 right-4 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
							)}
						</div>
					</PopoverTrigger>
					{formMode !== "xml" && (
						<PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
							<div className="flex flex-col">
								<div className="relative border-b border-gray-200">
									<FaSearch className="absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
									<input
										placeholder="Buscar fornecedor..."
										value={searchTerm}
										onChange={e => setSearchTerm(e.target.value)}
										className="h-10 pl-9 pr-3 text-sm focus:outline-none w-full"
									/>
								</div>
								<div className="max-h-[200px] overflow-y-auto">
									{isLoading && <div className="py-3 px-3 text-sm text-gray-500">Carregando...</div>}
									{!isLoading && suppliers.length === 0 && (
										<div className="py-3 px-3 text-sm text-gray-500">
											{searchTerm ? "Nenhum fornecedor encontrado." : "Nenhum fornecedor cadastrado."}
										</div>
									)}
									{!isLoading &&
										suppliers.map(supplier => (
											<div
												key={supplier.id}
												className="py-2 px-3 cursor-pointer hover:bg-gray-50 text-sm border-b border-gray-100 last:border-b-0"
												onClick={() => handleSupplierSelect(supplier)}
											>
												{supplier.name}
											</div>
										))}
								</div>
							</div>
						</PopoverContent>
					)}
				</Popover>
			</div>
			{errors && <FormMessage>{errors.message}</FormMessage>}
		</FormItem>
	);
};

interface TextFieldProps<T extends FieldValues, TFieldName extends FieldPath<T>> {
	field: ControllerRenderProps<T, TFieldName>;
	formMode: "xml" | "manual";
	errors?: FieldError;
	label: string;
	placeholder: string;
	icon: React.ReactNode;
	inputRef?: React.RefObject<HTMLInputElement>;
}

const TextField = <T extends FieldValues, TFieldName extends FieldPath<T>>({
	field,
	formMode,
	errors,
	label,
	placeholder,
	icon,
	inputRef,
}: TextFieldProps<T, TFieldName>) => (
	<FormItem>
		<FormLabel>{label}:</FormLabel>
		<div className="relative">
			<FormControl className="relative h-[45px] flex items-center">
				<Input
					{...field}
					ref={inputRef}
					disabled={formMode === "xml"}
					readOnly={formMode === "xml"}
					type="text"
					placeholder={placeholder}
					className={`
						h-[45px] pl-10
						${
							formMode === "xml"
								? "bg-gray-100 text-gray-500 cursor-not-allowed"
								: "bg-white text-gray-900 border-gray-200 hover:border-gray-300 focus:border-mainColor focus:ring-1 focus:ring-mainColor"
						}
					`}
				/>
			</FormControl>
			{icon}
		</div>
		{errors && <FormMessage>{errors.message}</FormMessage>}
	</FormItem>
);

export const InvoiceForm: React.FC<InvoiceFormProps> = ({ methodsForm, formMode, firstInputRef }) => {
	const { errors } = methodsForm.formState;

	const handleSupplierChange = (supplier: Supplier | null) => {
		if (supplier) {
			methodsForm.setValue("invoice.supplier.id", supplier.id);
			methodsForm.setValue("invoice.supplier.name", supplier.name);
		}
	};

	return (
		<fieldset className="border-2 border-dashed rounded-[15px] border-gray-200 p-6 mb-6 bg-white shadow-sm">
			<legend className="flex items-center space-x-2 text-lg font-semibold text-gray-700 px-2">
				<FaFileInvoice className="text-gray-500" />
				<span>Informações da NF-e</span>
			</legend>

			<p className="flex items-center text-sm text-gray-500 mt-2 space-x-2">
				<FaInfoCircle className="text-gray-400" />
				<span>Adicione aqui as informações da NF-e.</span>
			</p>

			<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
				<FormField
					control={methodsForm.control}
					name="invoice.key"
					render={({ field }) => (
						<TextField<ICreateStock, "invoice.key">
							field={field}
							formMode={formMode}
							errors={errors.invoice?.key as FieldError | undefined}
							label="Chave"
							placeholder="Chave da NF-e"
							icon={<FaLock className="absolute top-1/2 left-4 transform -translate-y-1/2 text-gray-400" />}
							inputRef={firstInputRef}
						/>
					)}
				/>

				<FormField
					control={methodsForm.control}
					name="invoice.issueDate"
					render={({ field }) => <DateTimeField field={field} formMode={formMode} errors={errors} />}
				/>

				<FormField
					control={methodsForm.control}
					name="invoice.supplier.name"
					render={({ field }) => (
						<SupplierSelectField
							field={field}
							formMode={formMode}
							errors={errors.invoice?.supplier?.name as FieldError | undefined}
							onSupplierChange={handleSupplierChange}
						/>
					)}
				/>
			</div>
		</fieldset>
	);
};
