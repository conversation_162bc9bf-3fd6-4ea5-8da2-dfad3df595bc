import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IUserProfileDTO } from "../dtos/profile.dto";
import { AUTH_ROUTES } from "./endpoints";

export const profileRequest = async (): Promise<ApiResponse<IUserProfileDTO>> => {
	return await createRequest({
		path: AUTH_ROUTES.PROFILE,
		method: "GET",
		// timeout: 500,
	});
};
