import { formatCurrency } from "@/shared/lib/format-currency";
import { formatDate } from "@/shared/lib/format-date";
import { getOrderStatusConfig } from "@/shared/utils/order-status-config";
import { motion } from "framer-motion";
import { ShoppingCart, User } from "lucide-react";
import { IOrderDto } from "../dtos/order.dto";

interface OrdersListMobileProps {
	orders: IOrderDto[];
	isLoading?: boolean;
	onOrderClick?: (orderId: number) => void;
}

export function OrdersListMobile({ orders, isLoading, onOrderClick }: OrdersListMobileProps) {
	if (isLoading) {
		return (
			<div className="md:hidden space-y-4">
				{[...Array(3)].map((_, index) => (
					<div key={index} className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm animate-pulse">
						<div className="flex items-center justify-between mb-2">
							<div className="h-4 bg-gray-200 rounded w-24"></div>
							<div className="h-4 bg-gray-200 rounded w-20"></div>
						</div>
						<div className="flex items-center justify-between text-sm">
							<div className="h-3 bg-gray-200 rounded w-32"></div>
							<div className="h-3 bg-gray-200 rounded w-16"></div>
						</div>
					</div>
				))}
			</div>
		);
	}

	if (!orders || orders.length === 0) {
		return (
			<div className="md:hidden flex flex-col items-center justify-center py-12">
				<ShoppingCart size={48} className="text-gray-300 mb-4" />
				<p className="text-gray-500 text-center">Nenhum pedido encontrado</p>
			</div>
		);
	}

	return (
		<div className="md:hidden space-y-4">
			{orders.map(order => {
				const config = getOrderStatusConfig(order.status);
				const StatusIcon = config.icon;
				return (
					<motion.div
						key={order.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						className={`w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm ${
							onOrderClick ? "cursor-pointer hover:shadow-md hover:border-mainColor/30 transition-all" : ""
						}`}
						onClick={() => onOrderClick?.(order.id)}
					>
						<div className="flex items-center justify-between mb-2">
							<div className="flex items-center gap-2">
								<ShoppingCart size={16} className="text-gray-400" />
								<span className="font-medium text-gray-900">#{order.id}</span>
							</div>
							<span className={`inline-flex items-center gap-1 px-2 py-1 rounded-xl text-xs font-semibold ${config.color}`}>
								<StatusIcon size={14} /> {config.label}
							</span>
						</div>

						<div className="space-y-1 mb-3">
							<div className="flex items-center justify-between">
								<span className="text-sm text-gray-600 flex items-center gap-1">
									<User size={14} /> Cliente:
								</span>
								<span className="text-sm font-medium text-gray-900">{order.customer}</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-sm text-gray-600">Total:</span>
								<span className="text-sm font-bold text-green-600">{formatCurrency(order.total)}</span>
							</div>
						</div>

						<div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
							<span>Criado em: {formatDate(order.createdAt)}</span>
						</div>
					</motion.div>
				);
			})}
		</div>
	);
}
