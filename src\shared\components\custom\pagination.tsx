import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { calculateTotalPages } from "@/shared/lib/pagination.lib";
import { IPaginationData } from "@/shared/types/requests/requests.type";
import { motion } from "framer-motion";
import { FiChevronLeft, FiChevronRight, FiChevronsLeft, FiChevronsRight, FiList } from "react-icons/fi";

interface PaginationProps {
	page: number;
	itemsPerPage: number;
	onPageChange: (page: number) => void;
	onItemsPerPageChange: (itemsPerPage: number) => void;
	paginationData?: IPaginationData;
	totalPages?: number;
}

export const Pagination = ({
	page,
	itemsPerPage,
	onPageChange,
	onItemsPerPageChange,
	paginationData,
	totalPages: legacyTotalPages,
}: PaginationProps) => {
	const totalPages = paginationData ? calculateTotalPages(paginationData, itemsPerPage) : legacyTotalPages || 1;
	const mobileButtonClasses =
		"w-8 h-8 rounded-xl bg-white border-2 border-gray-200 enabled:hover:border-mainColor enabled:hover:text-mainColor disabled:opacity-40 disabled:cursor-not-allowed flex items-center justify-center";
	const desktopButtonClasses =
		"w-10 h-10 rounded-[20px] bg-white border-2 border-gray-200 enabled:hover:border-mainColor enabled:hover:text-mainColor disabled:opacity-40 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 enabled:hover:scale-105 enabled:hover:shadow-lg enabled:hover:shadow-mainColor/10";

	return (
		<motion.div
			className="flex flex-col sm:flex-row items-center justify-between gap-6 border-t border-gray-100 pt-3 pb-1"
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<div className="sm:hidden w-full flex items-center justify-between px-2">
				<Select
					value={String(itemsPerPage)}
					onValueChange={value => {
						onItemsPerPageChange(Number(value));
						onPageChange(1);
					}}
				>
					<SelectTrigger className="w-[60px] h-9 border-gray-200 rounded-xl bg-white">
						<SelectValue placeholder={String(itemsPerPage)} />
					</SelectTrigger>
					<SelectContent>
						{[5, 10, 20, 50].map(value => (
							<SelectItem key={value} value={String(value)} className="text-sm text-gray-600 hover:text-mainColor transition-colors">
								{value}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<div className="flex items-center gap-2">
					<CustomTooltip content="Primeira página">
						<motion.button
							onClick={() => onPageChange(1)}
							disabled={page === 1}
							className={mobileButtonClasses}
							whileTap={{ scale: 0.95 }}
							aria-label="Primeira página"
						>
							<FiChevronsLeft size={16} />
						</motion.button>
					</CustomTooltip>

					<CustomTooltip content="Página anterior">
						<motion.button
							onClick={() => onPageChange(page - 1)}
							disabled={page === 1}
							className={mobileButtonClasses}
							whileTap={{ scale: 0.95 }}
							aria-label="Página anterior"
						>
							<FiChevronLeft size={16} />
						</motion.button>
					</CustomTooltip>

					<div className="flex items-center justify-center min-w-[48px] h-8 bg-blue-50 rounded-xl px-2">
						<span className="text-sm font-medium text-mainColor">
							{page}/{totalPages}
						</span>
					</div>

					<CustomTooltip content="Próxima página">
						<motion.button
							onClick={() => onPageChange(page + 1)}
							disabled={page === totalPages}
							className={mobileButtonClasses}
							whileTap={{ scale: 0.95 }}
							aria-label="Próxima página"
						>
							<FiChevronRight size={16} />
						</motion.button>
					</CustomTooltip>

					<CustomTooltip content="Última página">
						<motion.button
							onClick={() => onPageChange(totalPages)}
							disabled={page === totalPages}
							className={mobileButtonClasses}
							whileTap={{ scale: 0.95 }}
							aria-label="Última página"
						>
							<FiChevronsRight size={16} />
						</motion.button>
					</CustomTooltip>
				</div>
			</div>

			<div className="hidden sm:flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
				<motion.div
					className="flex items-center gap-3 bg-gradient-to-r from-gray-50 to-white px-5 py-2.5 rounded-[20px] shadow-sm"
					whileHover={{ scale: 1.02 }}
					transition={{ type: "spring", stiffness: 400, damping: 10 }}
				>
					<div className="flex items-center gap-2">
						<div className="w-8 h-8 rounded-[15px] bg-gray-100 flex items-center justify-center">
							<FiList className="text-mainColor" size={16} />
						</div>
						<span className="text-sm font-medium text-gray-700 whitespace-nowrap">Itens por página:</span>
					</div>

					<Select
						value={String(itemsPerPage)}
						onValueChange={value => {
							onItemsPerPageChange(Number(value));
							onPageChange(1);
						}}
					>
						<SelectTrigger className="w-[80px] border-2 border-gray-200 focus:ring-mainColor focus:ring-offset-2 focus:ring-offset-gray-50 rounded-[15px] bg-white">
							<SelectValue placeholder="Selecione" />
						</SelectTrigger>
						<SelectContent>
							{[5, 10, 20, 50].map(value => (
								<SelectItem
									key={value}
									value={String(value)}
									className="text-sm text-gray-600 hover:text-mainColor transition-colors"
								>
									{value}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</motion.div>

				<motion.div
					className="flex items-center px-5 py-2.5 bg-mainColor/10 rounded-[20px] min-w-[140px] justify-center shadow-sm backdrop-blur-sm"
					whileHover={{ scale: 1.02 }}
					transition={{ type: "spring", stiffness: 400, damping: 10 }}
				>
					<span className="text-sm font-semibold text-mainColor whitespace-nowrap">
						Página {page} de {totalPages}
					</span>
				</motion.div>
			</div>

			<div className="hidden sm:flex items-center gap-3 bg-gradient-to-r from-gray-50 to-white p-2 rounded-[25px] shadow-sm">
				<CustomTooltip content="Primeira página">
					<motion.button
						onClick={() => onPageChange(1)}
						disabled={page === 1}
						className={desktopButtonClasses}
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						transition={{ type: "spring", stiffness: 400, damping: 10 }}
						aria-label="Primeira página"
					>
						<FiChevronsLeft size={20} className="text-current" />
					</motion.button>
				</CustomTooltip>

				<CustomTooltip content="Página anterior">
					<motion.button
						onClick={() => onPageChange(page - 1)}
						disabled={page === 1}
						className={desktopButtonClasses}
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						transition={{ type: "spring", stiffness: 400, damping: 10 }}
						aria-label="Página anterior"
					>
						<FiChevronLeft size={20} className="text-current" />
					</motion.button>
				</CustomTooltip>

				<div className="w-[2px] h-8 bg-gradient-to-b from-gray-200 to-transparent rounded-[20px] mx-1"></div>

				<CustomTooltip content="Próxima página">
					<motion.button
						onClick={() => onPageChange(page + 1)}
						disabled={page === totalPages}
						className={desktopButtonClasses}
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						transition={{ type: "spring", stiffness: 400, damping: 10 }}
						aria-label="Próxima página"
					>
						<FiChevronRight size={20} className="text-current" />
					</motion.button>
				</CustomTooltip>

				<CustomTooltip content="Última página">
					<motion.button
						onClick={() => onPageChange(totalPages)}
						disabled={page === totalPages}
						className={desktopButtonClasses}
						whileHover={{ scale: 1.05 }}
						whileTap={{ scale: 0.95 }}
						transition={{ type: "spring", stiffness: 400, damping: 10 }}
						aria-label="Última página"
					>
						<FiChevronsRight size={20} className="text-current" />
					</motion.button>
				</CustomTooltip>
			</div>
		</motion.div>
	);
};
