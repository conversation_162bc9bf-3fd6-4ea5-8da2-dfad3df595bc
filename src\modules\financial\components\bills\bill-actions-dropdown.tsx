import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/components/ui/dropdown-menu";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import { useState } from "react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { useDeleteBill } from "../../hooks/bills/delete.hook";
import { canEditBill } from "../../utils/bill-status";
import { DeleteBillModal } from "./delete-bill-modal";
import { EditBillModal } from "./edit-bill-modal";

interface BillActionsDropdownProps {
	bill: IBillDto;
}

export const BillActionsDropdown = ({ bill }: BillActionsDropdownProps) => {
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
	const deleteBill = useDeleteBill();

	const handleDelete = async () => {
		try {
			await deleteBill.mutateAsync({ id: bill.id });
			setIsDeleteModalOpen(false);
		} catch (error) {
			console.error("Erro ao excluir conta:", error);
		}
	};

	if (!canEditBill(bill.paymentDate)) {
		return null;
	}

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
						<MoreVertical size={18} className="text-gray-500" />
					</button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="w-[160px] bg-white">
					<DropdownMenuItem onClick={() => setIsEditModalOpen(true)} className="cursor-pointer">
						<Pencil size={16} className="mr-2" />
						Editar
					</DropdownMenuItem>
					<DropdownMenuItem onClick={() => setIsDeleteModalOpen(true)} className="cursor-pointer text-red-600">
						<Trash2 size={16} className="mr-2" />
						Excluir
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			<EditBillModal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} billId={bill.id} />
			<DeleteBillModal
				isOpen={isDeleteModalOpen}
				onClose={() => setIsDeleteModalOpen(false)}
				onConfirm={handleDelete}
				isLoading={deleteBill.isPending}
			/>
		</>
	);
};
