import { IProductList, IProductListDTO } from "@/modules/product/dtos/product/get-all-products.dto";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Button } from "@/shared/components/ui/button";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useNavigate } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { DollarSign, Edit, Package, Tag, Trash2 } from "lucide-react";

interface ProductCardMobileProps {
	data: ApiResponse<IProductList> | undefined;
	onDelete?: (product: IProductListDTO) => void;
}

export const ProductCardsMobile = ({ data: list, onDelete }: ProductCardMobileProps) => {
	const navigate = useNavigate();

	const getQuantityColor = (quantity: number) => {
		if (quantity > 100) return "text-mainColor bg-mainColor/10";
		if (quantity <= 100 && quantity > 10) return "text-yellow-700 bg-yellow-50";
		return "text-red-700 bg-red-50";
	};

	return (
		<div className="block md:hidden space-y-3">
			{list?.success &&
				list.data.data.map((item: IProductListDTO) => (
					<motion.div
						key={item.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						className="w-full rounded-2xl border border-gray-100 bg-white p-4 shadow-sm hover:shadow-md transition-shadow duration-200"
					>
						<header className="flex items-start justify-between gap-3 mb-3">
							<div className="flex-1 min-w-0">
								<h3 className="line-clamp-2 text-sm font-semibold text-gray-800 leading-5 mb-1">{item.name}</h3>
								{item.barcode && <p className="text-xs text-gray-500 font-mono">Código: {item.barcode}</p>}
							</div>

							<motion.span
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								className={`inline-flex shrink-0 items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium ${
									item.status ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
								}`}
							>
								<span className={`h-1.5 w-1.5 rounded-full ${item.status ? "bg-green-500" : "bg-red-500"}`} />
								{item.status ? "Em estoque" : "Fora de estoque"}
							</motion.span>
						</header>

						<div className="mb-3">
							<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
								<Tag size={12} className="shrink-0" />
								<span>Categoria</span>
							</div>
							{item.category ? (
								<span className="inline-flex items-center px-2.5 py-1 text-xs rounded-full bg-mainColor/10 text-mainColor font-medium">
									{item.category}
								</span>
							) : (
								<span className="inline-flex items-center px-2.5 py-1 text-xs rounded-full bg-gray-100 text-gray-500">
									Sem categoria
								</span>
							)}
						</div>
						<div className="mb-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="bg-gray-50 rounded-xl p-3">
									<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
										<DollarSign size={12} className="shrink-0" />
										<span>Preço</span>
									</div>
									<div className="font-semibold text-sm text-gray-800">
										{new Intl.NumberFormat("pt-BR", {
											style: "currency",
											currency: "BRL",
											minimumFractionDigits: 2,
											maximumFractionDigits: 2,
										}).format(item.price)}
									</div>
								</div>

								<div className="bg-gray-50 rounded-xl p-3">
									<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
										<Package size={12} className="shrink-0" />
										<span>Quantidade</span>
									</div>
									<motion.span
										initial={{ scale: 0.8, opacity: 0 }}
										animate={{ scale: 1, opacity: 1 }}
										className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-semibold ${getQuantityColor(item.quantity)}`}
									>
										{item.quantity} un
									</motion.span>
								</div>
							</div>
						</div>

						{/* ---------- Ações ---------- */}
						<footer className="flex justify-end gap-3 border-t border-gray-100 pt-3">
							<CustomTooltip content="Editar Produto">
								<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
									<Button
										variant="ghost"
										size="icon"
										className="h-9 w-9 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
										onClick={() => navigate({ to: `/produtos/${item.id}` })}
									>
										<Edit className="h-4 w-4" />
									</Button>
								</motion.div>
							</CustomTooltip>
							<CustomTooltip content="Excluir Produto">
								<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
									<Button
										variant="ghost"
										size="icon"
										className="h-9 w-9 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
										onClick={() => onDelete && onDelete(item)}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</motion.div>
							</CustomTooltip>
						</footer>
					</motion.div>
				))}
		</div>
	);
};
