import { LucideIcon } from "lucide-react";
import "./styles.css";

interface TitleProps {
	title: string;
	icon?: LucideIcon;
}

export const TitleWithLine = ({ title, icon: Icon }: TitleProps) => {
	return (
		<div className="title-container relative flex items-center gap-3 md:gap-4 mb-4 group">
			<div className="flex items-center gap-2 md:gap-3">
				{Icon && (
					<div className="flex items-center justify-center w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-mainColor/10 to-mainColor/5 rounded-lg border border-mainColor/10 transition-all duration-300 hover:bg-mainColor/20 hover:scale-105 hover:shadow-lg hover:shadow-mainColor/20">
						<Icon size={20} className="text-mainColor transition-all duration-300 group-hover:scale-110" />
					</div>
				)}

				<h2 className="font-semibold text-slate-700 tracking-tight whitespace-nowrap text-base md:text-lg lg:text-xl xl:text-2xl leading-none transition-colors duration-300 group-hover:text-slate-800">
					{title}
				</h2>
			</div>

			<div className="shimmer-line flex-1 relative h-[1px] bg-gradient-to-r from-slate-200 via-mainColor/30 to-transparent overflow-hidden rounded-full">
				<div className="absolute inset-0 bg-gradient-to-r from-transparent via-mainColor/60 to-transparent animate-shimmer" />
			</div>
			<div className="relative">
				<div className="w-2 h-2 bg-gradient-to-br from-mainColor/60 to-mainColor/30 rounded-full animate-pulse" />
				<div className="absolute inset-0 w-2 h-2 bg-mainColor/20 rounded-full animate-ping" />
			</div>
		</div>
	);
};
