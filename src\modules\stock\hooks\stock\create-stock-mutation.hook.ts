import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSet<PERSON>tom } from "jotai";
import { UseFieldArrayReturn, UseFormReset } from "react-hook-form";
import { toast } from "sonner";
import { createStockRequest } from "../../api/requests/create";
import { ICreateStockDto } from "../../dtos/create-stock.dto";
import { persistedStockAtom } from "../../states/create-stock.state";
import { ICreateStock } from "../../validators/create-stock.validator";
import { DEFAULT_STOCK_VALUES } from "./create-stock-form.hook";

const handleSuccess = (
	message: string,
	queryClient: ReturnType<typeof useQueryClient>,
	reset: UseFormReset<ICreateStock>,
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">,
	onSuccess?: () => void
) => {
	toast.dismiss();
	toast.success(message);
	queryClient.invalidateQueries({ queryKey: ["xml-upload"] });
	queryClient.invalidateQueries({ queryKey: ["stock"] });
	reset(DEFAULT_STOCK_VALUES);
	inventoryFieldArray.replace([]);
	onSuccess?.();
};

const handleError = (message: string): never => {
	toast.dismiss();
	toast.error(message);
	throw new Error(message);
};

export const useCreateStockMutation = ({
	reset,
	inventoryFieldArray,
	onSuccess,
}: {
	reset: UseFormReset<ICreateStock>;
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">;
	onSuccess?: () => void;
}) => {
	const queryClient = useQueryClient();
	const setPersistedStock = useSetAtom(persistedStockAtom);

	const mutationFn = async (items: ICreateStockDto) => {
		const response = await createStockRequest(items);
		if (response.success) {
			handleSuccess(response.data?.message, queryClient, reset, inventoryFieldArray, onSuccess);
			setPersistedStock(null);
			return response.data;
		}
		return handleError(response.data.message);
	};

	const { mutate: createStock, isPending } = useMutation({
		mutationKey: ["create-stock"],
		mutationFn,
	});

	return { createStock, isPending };
};
