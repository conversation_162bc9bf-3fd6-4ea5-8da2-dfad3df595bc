import { useQuery } from "@tanstack/react-query";
import { findOrderDetailedByIdRequest } from "../api/requests/find-detailed-by-id";
import { ORDERS_QUERY_KEYS } from "../data/query-keys";

export const useOrderFindDetailedById = (id: number) => {
	const { data, error, isLoading } = useQuery({
		queryKey: ORDERS_QUERY_KEYS.FIND_DETAILED_BY_ID(id),
		queryFn: () => findOrderDetailedByIdRequest(id),
		enabled: !!id,
	});

	return {
		data,
		error,
		isLoading,
	};
};
