import { IPaginatedResponse } from "@/shared/types/requests/requests.type";

export interface IFindAllTransactionsParamsDto {
	page: number;
	limit: number;
	date?: string;
	accountId?: number;
	description?: string;
}

export interface ITransactionsDto extends IPaginatedResponse<ITransactionDto> {
	total: number;
}

export interface ITransactionDto {
	id: number;
	value: string;
	description: string;
	date: string;
	account: string;
}
