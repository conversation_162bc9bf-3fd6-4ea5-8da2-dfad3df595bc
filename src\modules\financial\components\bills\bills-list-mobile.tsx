import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { But<PERSON> } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { Calendar, DollarSign, Edit, Receipt, Timer, Trash2, User } from "lucide-react";
import { useState } from "react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { useDeleteBill } from "../../hooks/bills/delete.hook";
import { canEditBill, isBillPaid } from "../../utils/bill-status";
import { isPayableType } from "../../utils/bill-type-converter";
import { DeleteBillModal } from "./delete-bill-modal";
import { EditBillModal } from "./edit-bill-modal";

interface BillsListMobileProps {
	accounts: IBillDto[];
}

export function BillsListMobile({ accounts }: BillsListMobileProps) {
	const [editingBill, setEditingBill] = useState<number | null>(null);
	const [deletingBill, setDeletingBill] = useState<number | null>(null);
	const deleteBill = useDeleteBill();

	const handleDelete = async (billId: number) => {
		try {
			await deleteBill.mutateAsync({ id: billId });
			setDeletingBill(null);
		} catch (error) {
			console.error("Erro ao excluir conta:", error);
		}
	};
	const getStatusColor = (dueDate: string, isPaid: boolean) => {
		if (isPaid) return "text-green-700 bg-green-50";

		if (!dueDate) return "text-gray-700 bg-gray-50";

		const today = new Date();

		const datePart = dueDate.split(" ")[0];
		const [day, month, year] = datePart.split("/");
		const due = new Date(Number(year), Number(month) - 1, Number(day));

		const diffTime = due.getTime() - today.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays < 0) return "text-red-700 bg-red-50"; // Vencida
		if (diffDays <= 7) return "text-yellow-700 bg-yellow-50"; // Vence em breve
		return "text-blue-700 bg-blue-50"; // Em dia
	};

	const getStatusText = (dueDate: string, isPaid: boolean) => {
		if (isPaid) return "Pago";

		if (!dueDate) return "Sem vencimento";

		const today = new Date();
		const datePart = dueDate.split(" ")[0];
		const [day, month, year] = datePart.split("/");
		const due = new Date(Number(year), Number(month) - 1, Number(day));

		const diffTime = due.getTime() - today.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays < 0) return "Vencida";
		if (diffDays <= 7) return "Vence em breve";
		return "Em dia";
	};

	const formatDisplayValue = (value: string) => {
		if (value.includes("R$")) return value;

		const numericValue = Number(value.replace(/[^\d,-]/g, "").replace(",", "."));
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(numericValue || 0);
	};

	const formatDate = (dateString: string) => {
		if (!dateString) return "-";
		return dateString.split(" ")[0];
	};

	return (
		<div className="block md:hidden space-y-3">
			{accounts.map(account => (
				<motion.div
					key={account.id}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					className="w-full rounded-2xl border border-gray-100 bg-white p-4 shadow-sm hover:shadow-md transition-shadow duration-200"
				>
					<header className="flex items-start justify-between gap-3 mb-3">
						<div className="flex-1 min-w-0">
							<h3 className="line-clamp-2 text-sm font-semibold text-gray-800 leading-5 mb-1">{account.description}</h3>
							<p className="text-xs text-gray-500">ID: #{account.id}</p>
						</div>{" "}
						<motion.span
							initial={{ scale: 0.8, opacity: 0 }}
							animate={{ scale: 1, opacity: 1 }}
							className={`inline-flex shrink-0 items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium ${getStatusColor(
								account.dueDate,
								isBillPaid(account.paymentDate)
							)}`}
						>
							<span className={`h-1.5 w-1.5 rounded-full ${isBillPaid(account.paymentDate) ? "bg-green-500" : "bg-current"}`} />
							{getStatusText(account.dueDate, isBillPaid(account.paymentDate))}
						</motion.span>
					</header>
					{/* ---------- Tipo ---------- */}
					<div className="mb-3">
						<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
							<Receipt size={12} className="shrink-0" />
							<span>Tipo</span>
						</div>
						<span
							className={`inline-flex items-center px-2.5 py-1 text-xs rounded-full font-medium ${
								isPayableType(account.type) ? "bg-red-50 text-red-700" : "bg-green-50 text-green-700"
							}`}
						>
							{isPayableType(account.type) ? "A pagar" : "A receber"}
						</span>
					</div>
					{/* ---------- Informações Principais ---------- */}
					<div className="mb-4">
						<div className="grid grid-cols-2 gap-4">
							<div className="bg-gray-50 rounded-xl p-3">
								<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
									<DollarSign size={12} className="shrink-0" />
									<span>Valor</span>
								</div>{" "}
								<div className="font-semibold text-sm text-gray-800">{formatDisplayValue(account.value)}</div>
							</div>{" "}
							<div className="bg-gray-50 rounded-xl p-3">
								<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
									<Calendar size={12} className="shrink-0" />
									<span>Vencimento</span>
								</div>
								<div className="text-sm font-medium text-gray-800">{account.dueDate ? formatDate(account.dueDate) : "-"}</div>
							</div>
						</div>
					</div>{" "}
					{/* ---------- Informações Adicionais ---------- */}
					{account.person && (
						<div className="mb-3">
							<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
								<User size={12} className="shrink-0" />
								<span>Pessoa</span>
							</div>
							<span className="text-xs text-gray-700 font-medium">{account.person}</span>
						</div>
					)}
					{account.account && (
						<div className="mb-3">
							<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
								<Receipt size={12} className="shrink-0" />
								<span>Conta</span>
							</div>
							<span className="text-xs text-gray-700 font-medium">{account.account}</span>
						</div>
					)}{" "}
					{account.paymentDate && account.paymentDate.trim() !== "" && (
						<div className="mb-3">
							<div className="flex items-center gap-2 text-xs text-gray-500 mb-1">
								<Timer size={12} className="shrink-0" />
								<span>Data de Pagamento</span>
							</div>
							<span className="text-xs text-gray-700 font-medium">{formatDate(account.paymentDate)}</span>
						</div>
					)}{" "}
					{/* ---------- Ações ---------- */}
					<footer className="flex justify-end gap-3 border-t border-gray-100 pt-3">
						{canEditBill(account.paymentDate) && (
							<>
								<CustomTooltip content="Editar Conta">
									<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
										<Button
											variant="ghost"
											size="icon"
											className="h-9 w-9 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
											onClick={() => setEditingBill(account.id)}
										>
											<Edit className="h-4 w-4" />
										</Button>
									</motion.div>
								</CustomTooltip>
								<CustomTooltip content="Excluir Conta">
									<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
										<Button
											variant="ghost"
											size="icon"
											className="h-9 w-9 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
											onClick={() => setDeletingBill(account.id)}
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</motion.div>
								</CustomTooltip>
							</>
						)}
					</footer>
				</motion.div>
			))}

			{/* Modais */}
			{editingBill && <EditBillModal isOpen={editingBill !== null} onClose={() => setEditingBill(null)} billId={editingBill} />}
			{deletingBill && (
				<DeleteBillModal
					isOpen={deletingBill !== null}
					onClose={() => setDeletingBill(null)}
					onConfirm={() => handleDelete(deletingBill)}
					isLoading={deleteBill.isPending}
				/>
			)}
		</div>
	);
}
