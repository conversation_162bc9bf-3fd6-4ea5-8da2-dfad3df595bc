import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { useNavigate } from "@tanstack/react-router";
import { Home, List } from "lucide-react";
import { IoAdd } from "react-icons/io5";
import { FiltersSection } from "../components/product-list/filters/filters-section";
import { TableProducts } from "../components/product-list/table-products";
import { useProductFilters } from "../hooks/use-product-filters.hook";

export const ProductsPage = () => {
	const methods = useProductFilters();
	const { page, itemsPerPage, handleItemsPerPageChange, handlePageChange } = usePagination();
	const navigate = useNavigate();

	return (
		<MainSkeletonContainer
			iconTitle={List}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{ href: "/produtos", label: "Produtos", icon: List }}
			pageTitle="Lista de produtos"
		>
			<FiltersSection methods={methods.methods} />
			<TableProducts
				filter={methods.debouncedFilter}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
			<button
				onClick={() => navigate({ to: "/produtos/adicionar" })}
				className="md:hidden fixed bottom-24 right-4 z-[60] bg-mainColor text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl border-4 border-white hover:scale-105 active:scale-95 transition-all duration-200"
				aria-label="Adicionar novo produto"
			>
				<IoAdd size={28} />
			</button>
		</MainSkeletonContainer>
	);
};
