import { MovementsFilters } from "@/modules/stock/pages/stock-movements";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Calendar, FileText, Filter, RefreshCw, TrendingUp } from "lucide-react";
import { useState } from "react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { CreateMovementModal } from "../create-movement-modal";

export const StockMovementFiltersSection = ({ methods }: { methods: UseFormReturn<MovementsFilters> }) => {
	const { reset } = methods;
	const [isModalOpen, setIsModalOpen] = useState(false);

	const handleReset = () => {
		reset({
			search: "",
			date: "",
		});
	};

	return (
		<>
			<CardLMPContainer
				icon={<Filter size={22} className="text-gray-500" />}
				title="Filtros"
				description="Utilize os filtros abaixo para refinar sua busca."
				actions={
					<Button
						className="
            hidden
            md:flex
            items-center
            gap-2
            bg-mainColor
            text-white
            px-4
            py-2
            rounded-[10px]
            h-[45px]
            text-sm
            font-medium
            hover:bg-mainColor/90
            transition-colors
            shadow-sm
          "
						onClick={() => setIsModalOpen(true)}
					>
						<TrendingUp size={18} />
						Nova Movimentação
					</Button>
				}
			>
				<FormProvider {...methods}>
					<form className="flex flex-col gap-3 md:flex-row md:items-end">
						<div className="w-full md:w-1/2">
							<label className="block text-sm font-medium text-gray-600 mb-1">NFe</label>
							<div className="relative">
								<Input
									placeholder="Ex.: **********..."
									className="w-full h-[45px] rounded-[10px] pl-10"
									{...methods.register("search")}
								/>
								<FileText className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
							</div>
						</div>

						<div className="w-full md:w-1/2">
							<Controller
								control={methods.control}
								name="date"
								render={({ field }) => (
									<div className="relative">
										<DatePickerInput
											className="w-full"
											field={field}
											label="Data de Expiração"
											labelClassName="text-sm font-medium text-gray-700"
											calendarClassName="z-[9999]"
											inputDateClassName="w-full mt-1 pl-10 pr-4 h-[45px] border border-gray-300 rounded-[10px] text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0197B2] focus:border-transparent"
											placeholder="Selecione uma data"
										/>
										<Calendar className="absolute left-3 top-[38px] h-4 w-4 text-gray-400 pointer-events-none" />
									</div>
								)}
							/>
						</div>

						<div className="w-full md:w-auto">
							<Button
								type="button"
								variant="outline"
								onClick={handleReset}
								className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2"
							>
								<RefreshCw size={18} className="text-mainColor" />
								<span>Resetar Filtros</span>
							</Button>{" "}
						</div>
					</form>

					<div className="md:hidden mt-3 pt-3 border-t border-gray-100">
						<Button
							className="w-full flex items-center justify-center gap-2 bg-mainColor text-white h-[45px] rounded-[10px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
							onClick={() => setIsModalOpen(true)}
						>
							<IoAdd size={18} />
							Nova Movimentação
						</Button>
					</div>
				</FormProvider>
			</CardLMPContainer>

			<CreateMovementModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
		</>
	);
};
