import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowDownCircle, ArrowUpCircle, BadgeDollarSign, Calendar, CheckCircle, Clock, CreditCard, User, XCircle } from "lucide-react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { getBankIcon } from "../../utils/get-bank-icon";
import { isPayableType } from "../../utils/bill-type-converter";
import { BillActionsDropdown } from "./bill-actions-dropdown";

export type AccountStatus = "paid" | "pending" | "overdue" | "canceled";

interface BillsTableProps {
	accounts: IBillDto[];
}

export function BillsTable({ accounts }: BillsTableProps) {
	const statusConfig = {
		paid: { classes: "bg-green-50 text-green-700", text: "Pago", icon: CheckCircle },
		pending: { classes: "bg-amber-50 text-amber-700", text: "Pendente", icon: Clock },
		overdue: { classes: "bg-red-50 text-red-700", text: "Vencido", icon: XCircle },
		canceled: { classes: "bg-gray-50 text-gray-700", text: "Cancelado", icon: XCircle },
		default: { classes: "bg-gray-50 text-gray-700", text: "Pendente", icon: Clock },
	};

	const getAccountStatus = (account: IBillDto): AccountStatus => {
		if (account.paymentDate) return "paid";

		if (account.dueDate) {
			const dueDate = new Date(account.dueDate.split(" ")[0].split("/").reverse().join("-"));
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (dueDate < today) return "overdue";
		}

		return "pending";
	};

	const getTypeIcon = (type: string) => {
		if (isPayableType(type)) {
			return <ArrowDownCircle size={16} className="text-red-600 mr-1" />;
		}
		return <ArrowUpCircle size={16} className="text-green-600 mr-1" />;
	};

	const getValueColor = (type: string) => {
		return isPayableType(type) ? "text-red-600" : "text-green-600";
	};
	return (
		<AnimatePresence mode="wait">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0, y: -20 }}
				transition={{ duration: 0.2 }}
				className="border rounded-[15px] border-gray-200 overflow-hidden"
			>
				<div className="overflow-x-auto">
					<Table className="w-full min-w-[1000px] rounded-[15px] text-sm">
						<TableHeader className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
							<TableRow>
								<TableHead className="text-left font-semibold min-w-[120px] px-3">Descrição</TableHead>
								<TableHead className="text-center font-semibold min-w-[100px] px-2">Tipo</TableHead>
								<TableHead className="text-right font-semibold min-w-[100px] px-3">Valor</TableHead>
								<TableHead className="text-center font-semibold min-w-[110px] px-2">Vencimento</TableHead>
								<TableHead className="text-center font-semibold min-w-[110px] px-2">Pagamento</TableHead>
								<TableHead className="text-left font-semibold min-w-[120px] px-3">Pessoa</TableHead>
								<TableHead className="text-left font-semibold min-w-[120px] px-3">Conta</TableHead>
								<TableHead className="text-center font-semibold min-w-[90px] px-2">Status</TableHead>
								<TableHead className="text-center font-semibold min-w-[70px] px-2">Ações</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{accounts.map(account => {
								const status = getAccountStatus(account);
								const config = statusConfig[status] ?? statusConfig.default;
								const StatusIcon = config.icon;

								return (
									<TableRow key={account.id} className="hover:bg-gray-50 transition-colors">
										<TableCell className="text-left font-medium px-3">
											<div className="truncate max-w-[120px]" title={account.description}>
												{account.description}
											</div>
										</TableCell>
										<TableCell className="text-center px-2">
											<div className="flex items-center justify-center gap-1">
												{getTypeIcon(account.type)}
												<span
													className={`hidden sm:inline ${isPayableType(account.type) ? "text-red-600" : "text-green-600"}`}
												>
													{account.type}
												</span>
											</div>
										</TableCell>
										<TableCell className={`text-right font-bold px-3 ${getValueColor(account.type)}`}>
											<div className="flex items-center justify-end gap-1">
												<BadgeDollarSign size={16} className={getValueColor(account.type)} />
												<span className="whitespace-nowrap">{account.value}</span>
											</div>
										</TableCell>
										<TableCell className="text-center px-2">
											{account.dueDate ? (
												<div className="flex items-center justify-center gap-1">
													<Calendar size={14} className="text-gray-500 flex-shrink-0" />
													<span className="text-xs whitespace-nowrap">{account.dueDate.split(" ")[0]}</span>
												</div>
											) : (
												<span className="text-gray-400">-</span>
											)}
										</TableCell>
										<TableCell className="text-center px-2">
											{account.paymentDate ? (
												<div className="flex items-center justify-center gap-1">
													<Calendar size={14} className="text-green-500 flex-shrink-0" />
													<span className="text-xs whitespace-nowrap">{account.paymentDate.split(" ")[0]}</span>
												</div>
											) : (
												<span className="text-gray-400">-</span>
											)}
										</TableCell>
										<TableCell className="text-left px-3">
											<div className="flex items-center gap-1">
												<User size={14} className="text-gray-500 flex-shrink-0" />
												<span className="truncate max-w-[100px] text-xs" title={account.person}>
													{account.person || "-"}
												</span>
											</div>
										</TableCell>
										<TableCell className="text-left px-3">
											{account.account ? (
												<div className="flex items-center gap-1">
													{getBankIcon(account.account)}
													<span className="truncate max-w-[100px] text-xs" title={account.account}>
														{account.account}
													</span>
												</div>
											) : (
												<div className="flex items-center gap-1">
													<CreditCard size={14} className="text-gray-400 flex-shrink-0" />
													<span className="text-gray-400 text-xs">-</span>
												</div>
											)}
										</TableCell>
										<TableCell className="text-center px-2">
											<span
												className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium whitespace-nowrap ${config.classes}`}
											>
												<StatusIcon size={10} className="mr-1 flex-shrink-0" />
												<span className="hidden sm:inline">{config.text}</span>
											</span>
										</TableCell>
										<TableCell className="text-center px-2">
											<div className="flex items-center justify-center">
												<BillActionsDropdown bill={account} />
											</div>
										</TableCell>
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
