import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { But<PERSON> } from "@/shared/components/ui/button";
import { ISuccessResponse } from "@/shared/types/requests/requests.type";
import { motion } from "framer-motion";
import { Tag, Trash2 } from "lucide-react";
import { ICategoryFindDto } from "../../../dtos/category/find";
import { ICategoryFindAllDto } from "../../../dtos/category/find-all.dto";

interface CategoryCardsMobileProps {
	data: ISuccessResponse<ICategoryFindAllDto> | undefined;
	onDelete?: (category: ICategoryFindDto) => void;
}

export const CategoryCardsMobile = ({ data, onDelete }: CategoryCardsMobileProps) => {
	const categories = data?.success ? data.data.data : [];

	if (categories.length === 0) {
		return (
			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex flex-col items-center justify-center py-12 text-gray-500">
				<div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
					<Tag className="w-8 h-8 text-gray-400" />
				</div>
				<p className="text-lg font-medium text-gray-600 mb-2">Nenhuma categoria encontrada</p>
				<p className="text-sm text-gray-500 text-center max-w-xs">Adicione uma nova categoria para começar a organizar seus produtos</p>
			</motion.div>
		);
	}

	return (
		<div className="grid grid-cols-1 gap-4">
			{categories.map((category: ICategoryFindDto) => (
				<motion.div
					key={category.id}
					initial={{ y: 20, opacity: 0 }}
					animate={{ y: 0, opacity: 1 }}
					transition={{ duration: 0.3, ease: "easeOut" }}
					className="bg-gradient-to-r from-white to-gray-50/30 border border-gray-200/50 rounded-[18px] p-5 shadow-sm hover:shadow-md transition-all duration-200"
				>
					{/* Header */}
					<div className="flex items-center justify-between mb-4">
						<div className="flex items-center gap-3">
							<motion.div
								className="h-11 w-11 rounded-[12px] bg-gradient-to-br from-mainColor/10 to-mainColor/5 flex items-center justify-center"
								whileHover={{ scale: 1.05 }}
								transition={{ type: "spring", stiffness: 400, damping: 10 }}
							>
								<Tag className="h-5 w-5 text-mainColor" />
							</motion.div>
							<div>
								<h3 className="font-semibold text-gray-800 text-lg leading-tight">{category.name}</h3>
								<p className="text-sm text-gray-500">Categoria</p>
							</div>
						</div>
					</div>

					{/* <div className="grid grid-cols-2 gap-4 mb-4">
						<div className="bg-white/70 rounded-[12px] p-3 border border-gray-100">
							<div className="flex items-center gap-2 mb-1">
								<Package className="h-4 w-4 text-mainColor" />
								<span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Produtos</span>
							</div>
							<p className="text-lg font-bold text-gray-800">{category.productCount || 0}</p>
						</div>

						<div className="bg-white/70 rounded-[12px] p-3 border border-gray-100">
							<div className="flex items-center gap-2 mb-1">
								<Users className="h-4 w-4 text-blue-500" />
								<span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Grupo</span>
							</div>
							<p className="text-sm font-medium text-gray-700 truncate">{category.groupName || "Sem grupo"}</p>
						</div>
					</div> */}

					<div className="flex justify-end pt-3 border-t border-gray-100">
						<CustomTooltip content="Excluir Categoria">
							<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
								<Button
									variant="ghost"
									size="icon"
									className="h-9 w-9 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
									onClick={() => onDelete && onDelete(category)}
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							</motion.div>
						</CustomTooltip>
					</div>
				</motion.div>
			))}
		</div>
	);
};
