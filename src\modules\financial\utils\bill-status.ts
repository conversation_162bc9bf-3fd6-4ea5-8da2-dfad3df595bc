export const isBillPaid = (paymentDate: string | null | undefined): boolean => {
	if (!paymentDate) return false;
	if (typeof paymentDate !== "string") return false;

	const trimmedDate = paymentDate.trim();
	if (trimmedDate === "") return false;
	if (trimmedDate === "null") return false;
	if (trimmedDate === "undefined") return false;

	return true;
};

export const canEditBill = (paymentDate: string | null | undefined): boolean => {
	return !isBillPaid(paymentDate);
};
