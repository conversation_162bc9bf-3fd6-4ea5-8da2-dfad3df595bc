// MenuItemGrid.tsx

import { IItemSidebar, ISubItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { shouldNavigateDirectly } from "@/shared/routes/module-routes.factory";
import { ChevronRight } from "lucide-react";
import { MenuItem } from "./menu-item-mobile";

interface MenuItemGridProps {
	readonly items: IItemSidebar[] | IItemSidebar["subItems"];
	readonly itemActive: IItemSidebar | null;
	readonly onItemClick: (item: IItemSidebar | ISubItemSidebar) => void;
	readonly activeClassName?: string;
	readonly inactiveClassName?: string;
}

export function MenuItemGrid({ items, itemActive, onItemClick, activeClassName, inactiveClassName }: MenuItemGridProps) {
	return (
		<>
			{items?.map(item => {
				const hasSubItems = item.subItems && item.subItems.length > 0;
				const shouldNavigateDirect = shouldNavigateDirectly(item.subItems);
				const showSubItemsIndicator = hasSubItems && !shouldNavigateDirect;
				const subItemsCount = item.subItems?.length || 0;

				return (
					<div key={item.id} className="relative">
						<MenuItem
							id={item.id}
							label={item.label}
							accessibleDescription={item.accessibleDescription}
							icon={item.Icon}
							isActive={itemActive?.id === item.id}
							onClick={() => onItemClick(item)}
							activeClassName={activeClassName}
							inactiveClassName={inactiveClassName}
						/>

						{showSubItemsIndicator && (
							<div className="absolute top-3 right-3 flex items-center gap-1">
								{subItemsCount > 1 && (
									<div className="bg-mainColor text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-semibold">
										{subItemsCount}
									</div>
								)}
								<div className="bg-mainColor/10 rounded-full p-1">
									<ChevronRight size={12} className="text-mainColor" />
								</div>
							</div>
						)}
					</div>
				);
			})}
		</>
	);
}
