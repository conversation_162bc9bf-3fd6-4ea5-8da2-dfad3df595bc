import { IPaginationData } from "../types/requests/requests.type";

export const calculateTotalPages = (pagination: IPaginationData | undefined, itemsPerPage: number): number => {
	if (!pagination) return 1;
	if (pagination.totalPages && pagination.totalPages > 0) {
		return pagination.totalPages;
	}
	if (pagination.total && pagination.total > 0) {
		const perPage = pagination.itemsPerPage || itemsPerPage;
		return Math.ceil(pagination.total / perPage);
	}
	return 1;
};

export const extractPaginationData = (response: unknown): IPaginationData => {
	if (
		typeof response === "object" &&
		response !== null &&
		"pagination" in response &&
		typeof (response as { pagination?: unknown }).pagination === "object" &&
		(response as { pagination?: unknown }).pagination !== null
	) {
		return (response as { pagination: IPaginationData }).pagination;
	}
	if (typeof response === "object" && response !== null && "total" in response && typeof (response as { total?: unknown }).total === "number") {
		return {
			total: (response as { total: number }).total,
		};
	}
	return {
		total: 0,
	};
};
