// MobileBottomBar.tsx
import { SidebarItems } from "@/layout/hooks/sidebar/get-items.hook";
import { useItemsSidebar } from "@/layout/hooks/sidebar/items-sidebar.hook";
import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { ORDERS_CONFIG, ORDERS_SUBITEM_IDS } from "@/modules/orders/data/orders-config";
import { PERSON_CONFIG, PERSON_SUBITEM_IDS } from "@/modules/person/data/person-config";
import { PRODUCT_CONFIG, PRODUCT_SUBITEM_IDS } from "@/modules/product/data/product-config";
import { motion } from "framer-motion";
import { LayoutGrid, LucideProps, Package, ShoppingCart, Users } from "lucide-react";
import { FiAlignCenter } from "react-icons/fi";
import { IconType } from "react-icons/lib";

interface MobileBottomBarProps {
	onToggleMenu: () => void;
	getItems: SidebarItems;
}

interface ItemMenuSidebarFooterProps {
	onClick: () => void;
	IconLucide: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>> | IconType;
	isActive?: boolean;
	label?: string;
}

export function ItemMenuSidebarFooter({ IconLucide: Icon, onClick, isActive = false, label }: ItemMenuSidebarFooterProps) {
	return (
		<motion.button
			onClick={onClick}
			className="flex flex-col items-center justify-center gap-1 p-3 rounded-xl transition-all duration-200 group relative"
			whileHover={{ scale: 1.02 }}
			whileTap={{ scale: 0.98 }}
		>
			{isActive && (
				<motion.div
					className="absolute -top-1 w-1.5 h-1.5 bg-mainColor rounded-full"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ duration: 0.2 }}
				/>
			)}
			<div
				className={`
				p-2 rounded-lg transition-all duration-200
				${isActive ? "bg-mainColor/20 text-mainColor" : "text-gray-500 group-hover:text-mainColor group-hover:bg-mainColor/10"}
			`}
			>
				<Icon size={20} />
			</div>
			{label && (
				<span
					className={`
					text-xs font-medium transition-colors duration-200
					${isActive ? "text-mainColor" : "text-gray-500 group-hover:text-mainColor"}
				`}
				>
					{label}
				</span>
			)}
		</motion.button>
	);
}

export function MobileBottomBar({ onToggleMenu, getItems }: MobileBottomBarProps) {
	const { handleItemActive, menuItems, itemActive } = useItemsSidebar(getItems);

	const handleClickItemMobile = ({ id, items }: { id: string; items: IItemSidebar[] }) => {
		const item = items.find(item => item.id === id);
		if (item) {
			handleItemActive(item);
		}
	};

	const handleClickSubItemMobile = (moduleConfig: IItemSidebar, subItemId: string) => {
		const subItem = moduleConfig.subItems?.find(sub => sub.id === subItemId);
		if (subItem) {
			handleItemActive(moduleConfig, subItem);
		}
	};

	const isModuleActive = (moduleId: string, subItemId?: string) => {
		if (subItemId) {
			return itemActive?.id === moduleId;
		}
		return itemActive?.id === moduleId;
	};

	return (
		<motion.div
			className="fixed bottom-0 left-0 right-0 mx-2 mb-2 rounded-2xl bg-white/95 backdrop-blur-md shadow-2xl border border-gray-200/50 h-20 flex items-center justify-around px-4 z-10"
			initial={{ y: 50, opacity: 0 }}
			animate={{ y: 0, opacity: 1 }}
			transition={{ duration: 0.3, ease: "easeOut" }}
		>
			<ItemMenuSidebarFooter
				IconLucide={LayoutGrid}
				onClick={() => handleClickItemMobile({ id: "dashboard-group", items: menuItems })}
				isActive={isModuleActive("dashboard-group")}
			/>
			<ItemMenuSidebarFooter
				IconLucide={Package}
				onClick={() => handleClickSubItemMobile(PRODUCT_CONFIG, PRODUCT_SUBITEM_IDS.ALL_PRODUCTS)}
				isActive={isModuleActive("product-group", PRODUCT_SUBITEM_IDS.ALL_PRODUCTS)}
			/>
			<ItemMenuSidebarFooter IconLucide={FiAlignCenter} onClick={onToggleMenu} />
			<ItemMenuSidebarFooter
				IconLucide={ShoppingCart}
				onClick={() => handleClickSubItemMobile(ORDERS_CONFIG, ORDERS_SUBITEM_IDS.ALL_ORDERS)}
				isActive={isModuleActive("orders-group", ORDERS_SUBITEM_IDS.ALL_ORDERS)}
			/>
			<ItemMenuSidebarFooter
				IconLucide={Users}
				onClick={() => handleClickSubItemMobile(PERSON_CONFIG, PERSON_SUBITEM_IDS.ALL_PERSONS)}
				isActive={isModuleActive("person-group", PERSON_SUBITEM_IDS.ALL_PERSONS)}
			/>
		</motion.div>
	);
}
