export interface IScrollToErrorOptions {
	behavior?: ScrollBehavior;
	block?: ScrollLogicalPosition;
	inline?: ScrollLogicalPosition;
}

export function scrollToElement(element: HTMLElement | null, options: IScrollToErrorOptions = {}): void {
	if (!element) return;

	const defaultOptions: ScrollIntoViewOptions = {
		behavior: "smooth",
		block: "center",
		inline: "nearest",
		...options,
	};

	element.scrollIntoView(defaultOptions);
}

export function findFirstErrorIndex(elements: (HTMLElement | null)[], errorIndexes: number[]): number {
	for (const errorIndex of errorIndexes) {
		if (elements[errorIndex]) {
			return errorIndex;
		}
	}
	return -1;
}

export function scrollToFirstError(elements: (HTMLElement | null)[], errorIndexes: number[], options: IScrollToErrorOptions = {}): void {
	const firstErrorIndex = findFirstErrorIndex(elements, errorIndexes);
	if (firstErrorIndex !== -1) {
		scrollToElement(elements[firstErrorIndex], options);
	}
}
