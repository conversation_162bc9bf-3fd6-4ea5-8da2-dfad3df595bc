import { useFindAllProduct } from "@/modules/product/hooks/product/find-all-product.hook";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Button } from "@/shared/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ISuccessResponse } from "@/shared/types/requests/requests.type";
import { useNavigate } from "@tanstack/react-router";
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUpDown, Box, Edit, Trash2, X } from "lucide-react";
import { useState } from "react";
import { IProductList, IProductListDTO } from "../../../dtos/product/get-all-products.dto";
import { useDeleteProduct } from "../../../hooks/product/delete-product.hook";
import { ProductCardsMobile } from "../product-card";
import "./check.css";
import { DeleteProductModal } from "./delete-product-modal";
import LoadingState from "./loading-state";
import NoRecordsState from "./no-records-state";

interface TableProductsProps {
	filter: string | undefined;
	page: number;
	itemsPerPage: number;
	onPageChange: (page: number) => void;
	onItemsPerPageChange: (itemsPerPage: number) => void;
}

const getQuantityColor = (quantity: number) => {
	if (quantity > 100) return "bg-mainColor/10 text-mainColor";
	if (quantity <= 100 && quantity > 10) return "bg-yellow-50 text-yellow-700";
	return "bg-red-50 text-red-700";
};

export const TableProducts = ({ filter, page, itemsPerPage, onPageChange, onItemsPerPageChange }: TableProductsProps) => {
	const { data, isLoading } = useFindAllProduct({ filter, page, limit: itemsPerPage });
	const [sorting, setSorting] = useState<SortingState>([]);
	const navigate = useNavigate();
	const { deleteProduct, isLoading: isDeleting } = useDeleteProduct();
	const [productToDelete, setProductToDelete] = useState<IProductListDTO | null>(null);
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

	const handleOpenDeleteModal = (product: IProductListDTO) => {
		setProductToDelete(product);
		setIsDeleteModalOpen(true);
	};
	const handleCloseDeleteModal = () => {
		setIsDeleteModalOpen(false);
		setProductToDelete(null);
	};
	const handleDeleteProduct = () => {
		if (!productToDelete) return;
		deleteProduct(productToDelete.id);
		handleCloseDeleteModal();
	};

	const columns: ColumnDef<IProductListDTO>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => {
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Produto</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="text-center font-medium">{row.getValue("name")}</div>,
		},
		{
			accessorKey: "category",
			header: ({ column }) => {
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Categoria</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => {
				const category = row.getValue("category") as string;
				return (
					<div className="text-center min-w-[300px]">
						{!category ? (
							<motion.div
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								className="inline-flex items-center gap-1.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
							>
								<X size={12} className="text-gray-500" />
								Sem categoria
							</motion.div>
						) : (
							<div className="text-gray-600 whitespace-normal px-4">{category}</div>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "price",
			header: ({ column }) => {
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group w-[150px]"
						onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Preço</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="text-center text-gray-600 w-[150px]">R$ {row.getValue("price")}</div>,
		},
		{
			accessorKey: "quantity",
			header: ({ column }) => {
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group w-[150px]"
						onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Quantidade</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => {
				const quantity = row.getValue("quantity") as number;
				return (
					<div className="text-center w-[150px]">
						<motion.div
							initial={{ scale: 0.8, opacity: 0 }}
							animate={{ scale: 1, opacity: 1 }}
							className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQuantityColor(quantity)}`}
						>
							{quantity} un
						</motion.div>
					</div>
				);
			},
		},
		{
			accessorKey: "status",
			header: () => <div className="text-center text-gray-700 font-semibold w-[150px]">Status</div>,
			cell: ({ row }) => (
				<div className="text-center w-[150px]">
					<motion.div
						initial={{ scale: 0.8, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						className={`inline-flex items-center gap-1.5 px-2.5 py-0.5 rounded-full text-xs font-medium ${
							row.getValue("status") ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
						}`}
					>
						<div className={`w-1.5 h-1.5 rounded-full ${row.getValue("status") ? "bg-green-500" : "bg-red-500"}`} />
						{row.getValue("status") ? "Em estoque" : "Fora de estoque"}
					</motion.div>
				</div>
			),
		},
		{
			id: "actions",
			header: () => <div className="text-right pr-5 text-gray-700 font-semibold">Ações</div>,
			cell: ({ row }) => (
				<div className="flex items-center justify-end gap-1 pr-5">
					<CustomTooltip content="Editar">
						<motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
								onClick={() => navigate({ to: `/produtos/${row.original.id}` })}
							>
								<Edit className="h-4 w-4" />
							</Button>
						</motion.div>
					</CustomTooltip>
					<CustomTooltip content="Excluir">
						<motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
								onClick={() => handleOpenDeleteModal(row.original)}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</motion.div>
					</CustomTooltip>
				</div>
			),
		},
	];

	const tableData = (data as ISuccessResponse<IProductList>)?.data?.data || [];

	const table = useReactTable({
		data: tableData,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		state: {
			sorting,
		},
	});

	return (
		<CardLMPContainer
			description="Acompanhe informações de produtos de forma rápida."
			icon={<Box size={22} className="text-gray-500" />}
			title="Visão Geral de produtos"
		>
			{isLoading ? (
				<LoadingState />
			) : !tableData.length ? (
				<NoRecordsState />
			) : (
				<>
					<div className="hidden md:block pb-2">
						{" "}
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								transition={{ duration: 0.2 }}
								className="border rounded-[15px] border-gray-200 overflow-hidden "
							>
								<Table className="w-full rounded-[15px] text-sm min-w-[700px]">
									<TableHeader className="sticky top-0 bg-gradient-to-r from-gray-50 to-white z-10 border-b border-gray-200">
										{table.getHeaderGroups().map(headerGroup => (
											<TableRow key={headerGroup.id} className="hover:bg-transparent">
												{headerGroup.headers.map(header => (
													<TableHead key={header.id} className="py-4 px-2" style={{ width: `${header.column.getSize()}%` }}>
														{header.isPlaceholder
															? null
															: flexRender(header.column.columnDef.header, header.getContext())}
													</TableHead>
												))}
											</TableRow>
										))}
									</TableHeader>
									<TableBody className="[&>tr:nth-child(even)]:bg-[#f9fbfc] [&>tr:hover]:bg-[#f1faff] [&>tr:hover]:cursor-pointer">
										{table.getRowModel().rows.map(row => (
											<TableRow key={row.id} className="hover:bg-gray-50 transition-colors">
												{row.getVisibleCells().map(cell => (
													<TableCell key={cell.id} className="py-3 px-2">
														{flexRender(cell.column.columnDef.cell, cell.getContext())}
													</TableCell>
												))}
											</TableRow>
										))}
									</TableBody>
								</Table>
							</motion.div>
						</AnimatePresence>
					</div>

					<div className="md:hidden">
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								transition={{ duration: 0.2 }}
							>
								<ProductCardsMobile data={data} onDelete={handleOpenDeleteModal} />
							</motion.div>
						</AnimatePresence>
					</div>
				</>
			)}

			<DeleteProductModal
				isOpen={isDeleteModalOpen}
				onClose={handleCloseDeleteModal}
				onConfirm={handleDeleteProduct}
				isPending={isDeleting}
				product={productToDelete}
			/>

			<Pagination
				totalPages={data?.success ? data?.data.total : 0}
				itemsPerPage={itemsPerPage}
				page={page}
				onPageChange={onPageChange}
				onItemsPerPageChange={onItemsPerPageChange}
			/>
		</CardLMPContainer>
	);
};
