import { buildQueryParams } from "@/shared/lib/build-query-params";

interface FindAllParams {
	page: number;
	limit: number;
	invoice?: string;
	expirationDate?: string | Date;
	status?: boolean;
}

interface FindAllMovementsParams {
	page: number;
	limit: number;
	search?: string;
	date?: string | Date;
}

interface UpdateParams {
	id: number;
}

export const STOCK_ROUTES = {
	CREATE: "/stock/create",
	XML_UPLOAD: "/stock/upload-xml",
	CREATE_MOVEMENT: "/stock/create-movement",
	FIND_ALL: (params: FindAllParams): string => {
		const { page, limit, invoice, expirationDate, status } = params;
		const baseUrl = `/stock/find-all/${page}/${limit}`;

		return buildQueryParams(baseUrl, {
			invoice,
			expirationDate,
			status,
		});
	},
	UPDATE: (params: UpdateParams): string => `/stock/${params.id}/update`,
	FIND_ALL_MOVEMENTS: (params: FindAllMovementsParams): string => {
		const { page, limit, search, date } = params;
		const baseUrl = `/stock/find-all-movements/${page}/${limit}`;
		return buildQueryParams(baseUrl, {
			search,
			date,
		});
	},
} as const;
