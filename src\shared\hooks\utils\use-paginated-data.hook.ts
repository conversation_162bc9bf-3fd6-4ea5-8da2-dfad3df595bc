import { useMemo } from "react";
import { calculateTotalPages, extractPaginationData } from "@/shared/lib/pagination.lib";
import { ApiResponse, IPaginationData } from "@/shared/types/requests/requests.type";

interface IUsePaginatedDataProps<T> {
	data: ApiResponse<T> | undefined;
	itemsPerPage: number;
}

interface IUsePaginatedDataReturn {
	paginationData: IPaginationData;
	totalPages: number;
	isLoading: boolean;
	hasData: boolean;
}

export const usePaginatedData = <T>({ data, itemsPerPage }: IUsePaginatedDataProps<T>): IUsePaginatedDataReturn => {
	const paginationData = useMemo(() => {
		if (!data?.success) {
			return { total: 0 };
		}

		return extractPaginationData(data.data);
	}, [data]);

	const totalPages = useMemo(() => {
		return calculateTotalPages(paginationData, itemsPerPage);
	}, [paginationData, itemsPerPage]);

	const isLoading = !data;
	const hasData = !!(
		data?.success &&
		typeof data.data === "object" &&
		data.data !== null &&
		Array.isArray((data.data as { data?: unknown[] })?.data) &&
		((data.data as { data?: unknown[] }).data?.length ?? 0) > 0
	);

	return {
		paginationData,
		totalPages,
		isLoading,
		hasData,
	};
};
