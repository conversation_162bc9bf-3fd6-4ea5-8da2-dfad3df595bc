import { useEffect, useMemo, useState } from "react";
import { INcmSearchResult } from "../types/ncm.types";
import { ncmSearchUtil } from "../utils/ncm-search.util";

interface IUseNcmSearchProps {
	searchTerm: string;
	limit?: number;
}

interface IUseNcmSearchReturn {
	results: INcmSearchResult[];
	isLoading: boolean;
	isDataLoaded: boolean;
	dataLength: number;
}

export const useNcmSearch = ({ searchTerm, limit = 50 }: IUseNcmSearchProps): IUseNcmSearchReturn => {
	const [isLoading, setIsLoading] = useState(!ncmSearchUtil.isDataLoaded());
	const [isDataLoaded, setIsDataLoaded] = useState(ncmSearchUtil.isDataLoaded());

	// Carrega os dados NCM na primeira vez
	useEffect(() => {
		const loadData = async () => {
			if (!ncmSearchUtil.isDataLoaded()) {
				setIsLoading(true);
				await ncmSearchUtil.ensureDataLoaded();
				setIsLoading(false);
				setIsDataLoaded(true);
			}
		};

		loadData();
	}, []);

	// Debounce da busca - reduzido para 150ms para ser mais responsivo
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 150);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Realiza a busca
	const results = useMemo(() => {
		if (!isDataLoaded || !debouncedSearchTerm.trim()) {
			return [];
		}

		return ncmSearchUtil.searchNcm(debouncedSearchTerm, limit);
	}, [debouncedSearchTerm, isDataLoaded, limit]);

	const dataLength = useMemo(() => ncmSearchUtil.getDataLength(), [isDataLoaded]);

	return {
		results,
		isLoading,
		isDataLoaded,
		dataLength,
	};
};
