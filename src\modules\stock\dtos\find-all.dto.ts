import { IPaginatedResponse } from "@/shared/types/requests/requests.type";

export interface IFindAllStock {
	id: number;
	expirationDate: string;
	status: boolean;
	invoice: string;
	product: string;
	quantity: number;
	description: string;
}

export interface IFindAllStockDTO extends IPaginatedResponse<IFindAllStock> {
	total: number;
}

export interface IFindAllFiltersDto {
	page: number;
	limit: number;
	expirationDate?: string;
	status?: string;
	invoice?: string;
}
