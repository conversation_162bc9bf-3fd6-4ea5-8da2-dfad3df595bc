import { IInvoiceBase, IProductBase } from "./base-stock";

export interface IProductDetails extends IProductBase {
	description?: string;
	categoryId?: number;
	package?: {
		name: string;
		barcode: string;
		code: string;
		quantityPerPackage: number;
		id?: number;
	};
}

export interface ISuplierCreateStockDTO extends IInvoiceBase {
	supplierId: number;
}

export interface IStockMovement {
	description?: string;
	quantity: number;
	product: IProductDetails;
}

export interface IInventory {
	id?: number;
	expirationDate?: string;
	stockMovement: IStockMovement;
}

export interface ICreateStockDto {
	invoice: ISuplierCreateStockDTO;
	inventories: IInventory[];
}
